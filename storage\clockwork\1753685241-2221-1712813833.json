{"id": "1753685241-2221-1712813833", "version": 1, "type": "request", "time": 1753685240.956076, "method": "GET", "url": "http://127.0.0.1:8000/login", "uri": "/login", "headers": {"host": ["127.0.0.1:8000"], "connection": ["keep-alive"], "upgrade-insecure-requests": ["1"], "user-agent": ["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"], "accept": ["text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7"], "sec-fetch-site": ["none"], "sec-fetch-mode": ["navigate"], "sec-fetch-user": ["?1"], "sec-fetch-dest": ["document"], "sec-ch-ua": ["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""], "sec-ch-ua-mobile": ["?0"], "sec-ch-ua-platform": ["\"Windows\""], "accept-encoding": ["gzip, deflate, br, zstd"], "accept-language": ["en-GB,en-US;q=0.9,en;q=0.8,ms;q=0.7"], "cookie": ["remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Ik52M3VabWhoUVowcmdKT3k4REhyZnc9PSIsInZhbHVlIjoiN29uVEJUdFRMVTBHdmdTa2J1U1FYODRuWmJFZGpUSkc3Z2ZDWDl0SUpZUGowbTAyaDR6MDlZTldqY2NpOUJhN3Y5RVVlK2ZLRHAyK3ZxQjhudDdvT2h5NjhDTWYycmRnb3RCYlZ1S1JuZThrY0RHaGxPdzlIN21EZTAraENhaXQ3NlMxbnNmRSt4d1diQThZQVh4OU85MlFrSkoyR045K1FOc3oweUM5SlgrVEFZc0h3a0YrR0dpZ0dTcWJQVVRteU9yamwvWldsNm41WFpZVDUvbHg5UFhvbXhRamcrUXFSckgvRHQzVGE4MD0iLCJtYWMiOiI4MzE4MWVkODlkMGI2ZGVlM2U1OTllMTJhMjE1ZjJiNDdmMjFlZTcyYmNkMWM0NjU2YTU4YTU4OTUyNDc5NDU4IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6ImV4aFlkQnlxRG9oRVlSTEFKY1pzcHc9PSIsInZhbHVlIjoicnVxWmpLemtETXlCUnJ6YzVPWjd6TnNEM2M4cjhCZEkremJqQmhxMDYyTDB5K3VncUQ5eEE0TW4wZFlEa2QvNmpJRUZ3NkNsaVZjQ0tWajYzd1J6MFJnYVBkaFF5eDR2VmdhS3pRdThoMHl4b2JjbTMzbWtlaTRycExJK09WZlEiLCJtYWMiOiI5NWRjNTgxMmNhYWUyODI0OWExNWE4NzFiODBlNTM2OWM2YTU3NzZhMGI2MTI3ZWU5Y2RhMTYwNTM3NDFlZTNhIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IjhuajdpOVZvc0MvQXZYSjhERmllUVE9PSIsInZhbHVlIjoib3hrM09sUVZ3K25LV1VVc2VVUjRpTUhaYmNlSlhHM0FUZWV6Zzg1V2lYNmRSQ251bkdJUjFmMXBJWEkxUGF3VmxNRlZvS1czS01WVWV0OU00WldlYlRianc3eDA5MThjZHVqUlc5RlQ5VGZWcVY2SExMRzBGcG1Fd1Q5bDFncngiLCJtYWMiOiI1MWI4ZDQ4MDZkODA3ODQ0MTNjZjFhZDQzNmQzNTQ5M2RhOWExZTRmNDQwNmMxZDI0ZDc4OTg2YmY5YmUyZDc3IiwidGFnIjoiIn0%3D"]}, "controller": "App\\Livewire\\Login", "getData": [], "postData": [], "requestData": "", "sessionData": {"_token": "3vH6wLKrSusROaeuO28CvWXBleMW483O5eZR06O0", "url": {"__type__": "array", "intended": "http://127.0.0.1:8000"}, "_previous": {"__type__": "array", "url": "http://127.0.0.1:8000/login"}, "_flash": {"__type__": "array", "old": {"__type__": "array"}, "new": {"__type__": "array"}}}, "authenticatedUser": null, "cookies": {"remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "eyJpdiI6Ik52M3VabWhoUVowcmdKT3k4REhyZnc9PSIsInZhbHVlIjoiN29uVEJUdFRMVTBHdmdTa2J1U1FYODRuWmJFZGpUSkc3Z2ZDWDl0SUpZUGowbTAyaDR6MDlZTldqY2NpOUJhN3Y5RVVlK2ZLRHAyK3ZxQjhudDdvT2h5NjhDTWYycmRnb3RCYlZ1S1JuZThrY0RHaGxPdzlIN21EZTAraENhaXQ3NlMxbnNmRSt4d1diQThZQVh4OU85MlFrSkoyR045K1FOc3oweUM5SlgrVEFZc0h3a0YrR0dpZ0dTcWJQVVRteU9yamwvWldsNm41WFpZVDUvbHg5UFhvbXhRamcrUXFSckgvRHQzVGE4MD0iLCJtYWMiOiI4MzE4MWVkODlkMGI2ZGVlM2U1OTllMTJhMjE1ZjJiNDdmMjFlZTcyYmNkMWM0NjU2YTU4YTU4OTUyNDc5NDU4IiwidGFnIjoiIn0=", "XSRF-TOKEN": "eyJpdiI6ImV4aFlkQnlxRG9oRVlSTEFKY1pzcHc9PSIsInZhbHVlIjoicnVxWmpLemtETXlCUnJ6YzVPWjd6TnNEM2M4cjhCZEkremJqQmhxMDYyTDB5K3VncUQ5eEE0TW4wZFlEa2QvNmpJRUZ3NkNsaVZjQ0tWajYzd1J6MFJnYVBkaFF5eDR2VmdhS3pRdThoMHl4b2JjbTMzbWtlaTRycExJK09WZlEiLCJtYWMiOiI5NWRjNTgxMmNhYWUyODI0OWExNWE4NzFiODBlNTM2OWM2YTU3NzZhMGI2MTI3ZWU5Y2RhMTYwNTM3NDFlZTNhIiwidGFnIjoiIn0=", "laravel_session": "eyJpdiI6IjhuajdpOVZvc0MvQXZYSjhERmllUVE9PSIsInZhbHVlIjoib3hrM09sUVZ3K25LV1VVc2VVUjRpTUhaYmNlSlhHM0FUZWV6Zzg1V2lYNmRSQ251bkdJUjFmMXBJWEkxUGF3VmxNRlZvS1czS01WVWV0OU00WldlYlRianc3eDA5MThjZHVqUlc5RlQ5VGZWcVY2SExMRzBGcG1Fd1Q5bDFncngiLCJtYWMiOiI1MWI4ZDQ4MDZkODA3ODQ0MTNjZjFhZDQzNmQzNTQ5M2RhOWExZTRmNDQwNmMxZDI0ZDc4OTg2YmY5YmUyZDc3IiwidGFnIjoiIn0="}, "responseTime": 1753685241.545628, "responseStatus": 200, "responseDuration": 589.5521640777588, "memoryUsage": 29360128, "middleware": ["web"], "databaseQueries": [{"query": "SELECT * FROM `sessions` WHERE `id` = 'l0VD2OOo0rMp6iBrKVBCgst7pCulgzvBPrJLkKbM' LIMIT 1", "duration": 36.13, "connection": "epss2", "time": 1753685241.428016, "trace": [{"call": "Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():167}()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware.php", "line": 19, "isVendor": true}, {"call": "Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php", "line": 21, "isVendor": true}, {"call": "Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php", "line": 31, "isVendor": true}, {"call": "Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php", "line": 21, "isVendor": true}, {"call": "Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php", "line": 51, "isVendor": true}, {"call": "Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php", "line": 27, "isVendor": true}, {"call": "Illuminate\\Http\\Middleware\\ValidatePostSize->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208, "isVendor": true}], "model": null, "tags": []}, {"query": "UPDATE `sessions` SET `payload` = 'YTo0OntzOjY6Il90b2tlbiI7czo0MDoiM3ZINndMS3JTdXNST2FldU8yOEN2V1hCbGVNVzQ4M081ZVpSMDZPMCI7czozOiJ1cmwiO2E6MTp7czo4OiJpbnRlbmRlZCI7czoyMToiaHR0cDovLzEyNy4wLjAuMTo4MDAwIjt9czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6Mjc6Imh0dHA6Ly8xMjcuMC4wLjE6ODAwMC9sb2dpbiI7fXM6NjoiX2ZsYXNoIjthOjI6e3M6Mzoib2xkIjthOjA6e31zOjM6Im5ldyI7YTowOnt9fX0=', `last_activity` = 1753685241, `user_id` = , `ip_address` = '127.0.0.1', `user_agent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' WHERE `id` = 'l0VD2OOo0rMp6iBrKVBCgst7pCulgzvBPrJLkKbM'", "duration": 12.58, "connection": "epss2", "time": 1753685241.5323792, "trace": [{"call": "Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():167}()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware.php", "line": 19, "isVendor": true}, {"call": "Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php", "line": 21, "isVendor": true}, {"call": "Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php", "line": 31, "isVendor": true}, {"call": "Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php", "line": 21, "isVendor": true}, {"call": "Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php", "line": 51, "isVendor": true}, {"call": "Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php", "line": 27, "isVendor": true}, {"call": "Illuminate\\Http\\Middleware\\ValidatePostSize->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208, "isVendor": true}], "model": null, "tags": []}], "databaseQueriesCount": 2, "databaseSlowQueries": 0, "databaseSelects": 1, "databaseInserts": 0, "databaseUpdates": 1, "databaseDeletes": 0, "databaseOthers": 0, "databaseDuration": 48.71, "cacheQueries": [], "cacheReads": 0, "cacheHits": 0, "cacheWrites": 0, "cacheDeletes": 0, "cacheTime": 0, "modelsActions": [], "modelsRetrieved": [], "modelsCreated": [], "modelsUpdated": [], "modelsDeleted": [], "redisCommands": [], "queueJobs": [], "timelineData": [{"description": "Controller", "start": 1753685241.398118, "end": 1753685241.545507, "duration": 147.38893508911133, "color": null, "data": null}], "log": [], "events": [], "routes": [{"method": "GET, HEAD", "uri": "login", "name": "login", "action": "App\\Livewire\\Login", "middleware": ["web"], "before": "", "after": ""}, {"method": "GET, HEAD", "uri": "home", "name": "home", "action": "App\\Livewire\\HomePage", "middleware": ["web", "auth:web", "log.activity"], "before": "", "after": ""}, {"method": "GET, HEAD", "uri": "profile", "name": "profile", "action": "App\\Livewire\\UserProfile", "middleware": ["web", "auth:web", "log.activity"], "before": "", "after": ""}, {"method": "GET, HEAD", "uri": "crm", "name": "crm", "action": "App\\Livewire\\Project\\CRM", "middleware": ["web", "auth:web", "log.activity", "route.permission"], "before": "", "after": ""}, {"method": "GET, HEAD", "uri": "ep", "name": "ep", "action": "App\\Livewire\\Project\\EPerolehan", "middleware": ["web", "auth:web", "log.activity", "route.permission"], "before": "", "after": ""}, {"method": "GET, HEAD", "uri": "report", "name": "report", "action": "App\\Livewire\\Project\\Report", "middleware": ["web", "auth:web", "log.activity", "route.permission"], "before": "", "after": ""}, {"method": "GET, HEAD", "uri": "bpm", "name": "bpm", "action": "App\\Livewire\\Project\\Bpm", "middleware": ["web", "auth:web", "log.activity", "route.permission"], "before": "", "after": ""}, {"method": "GET, HEAD", "uri": "settings", "name": "settings", "action": "App\\Livewire\\AppSetting", "middleware": ["web", "auth:web", "log.activity", "route.permission"], "before": "", "after": ""}], "notifications": [], "emailsData": [], "viewsData": [{"description": "Rendering a view", "start": 1753685241.497496, "end": 1753685241.497496, "duration": 0, "color": null, "data": {"name": "authentication.login", "data": {"__type__": "array", "enabledAuthMethods": {"__type__": "array", "0": "LDAP", "1": "CRM", "2": "EPSS"}, "form": {"__class__": "App\\Livewire\\Forms\\LoginForm", "*component": {"__class__": "App\\Livewire\\Login", "*__id": "9QpXBa7Jni6xHCrspnJs", "*__name": "login", "*listeners": {"__type__": "array"}, "*attributes": {"__class__": "Livewire\\Features\\SupportAttributes\\AttributeCollection", "*items": {"__type__": "array", "0": {"__class__": "Livewire\\Attributes\\Layout", "*component": {"__type__": "recursion"}, "*subTarget": null, "*subName": null, "*level": {"__class__": "Livewire\\Features\\SupportAttributes\\AttributeLevel", "name": "ROOT"}, "*levelName": null, "name": "layouts.guest", "params": {"__type__": "array"}}}, "*escapeWhenCastingToString": false}, "*withValidatorCallback": null, "*rulesFromOutside": {"__type__": "array"}, "*messagesFromOutside": {"__type__": "array"}, "*validationAttributesFromOutside": {"__type__": "array"}, "form": {"__type__": "recursion"}, "~crmCredentialError": false}, "*propertyName": "form", "*withValidatorCallback": null, "*rulesFromOutside": {"__type__": "array"}, "*messagesFromOutside": {"__type__": "array"}, "*validationAttributesFromOutside": {"__type__": "array"}, "user_name": "", "password": "", "remember": false}, "layoutConfig": {"__class__": "Livewire\\Features\\SupportPageComponents\\PageComponentConfig", "slots": {"__type__": "array"}, "viewContext": {"__class__": "Livewire\\Mechanisms\\HandleComponents\\ViewContext", "slots": {"__type__": "array"}, "pushes": {"__type__": "array"}, "prepends": {"__type__": "array"}, "sections": {"__type__": "array"}}, "response": null, "type": "component", "view": "layouts.guest", "slotOrSection": "slot", "params": {"__type__": "array"}}}}}, {"description": "Rendering a view", "start": 1753685241.507085, "end": 1753685241.507085, "duration": 0, "color": null, "data": {"name": "__components::4943bc92ebba41e8b0e508149542e0ad", "data": {"__type__": "array", "content": "<div wire:snapshot=\"{&quot;data&quot;:{&quot;form&quot;:[{&quot;user_name&quot;:&quot;&quot;,&quot;password&quot;:&quot;&quot;,&quot;remember&quot;:false},{&quot;class&quot;:&quot;App\\\\Livewire\\\\Forms\\\\LoginForm&quot;,&quot;s&quot;:&quot;form&quot;}]},&quot;memo&quot;:{&quot;id&quot;:&quot;9QpXBa7Jni6xHCrspnJs&quot;,&quot;name&quot;:&quot;login&quot;,&quot;path&quot;:&quot;login&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;children&quot;:[],&quot;scripts&quot;:[],&quot;assets&quot;:[],&quot;errors&quot;:[],&quot;locale&quot;:&quot;en&quot;},&quot;checksum&quot;:&quot;7fd3e5488cb10d61e5ef1b8aa1f4e9ffbf041ec345e3f5b5fbbbf8a4a31f46d2&quot;}\" wire:effects=\"[]\" wire:id=\"9QpXBa7Jni6xHCrspnJs\" class=\"grid lg:grid-cols-2 grow min-h-screen\">\n    <style>\n        .branded-bg {\n            background-image: url(http://127.0.0.1:8000/assets/media/images/2600x1600/1.png);\n        }\n\n        .dark .branded-bg {\n            background-image: url(http://127.0.0.1:8000/assets/media/images/2600x1600/1-dark.png);\n        }\n\n        .error-message {\n            color: rgb(239 68 68) !important;\n        }\n    </style>\n    <!-- Left: Login form container -->\n    <div class=\"flex justify-center items-center p-8 lg:p-10 order-2 lg:order-1\">\n        <div class=\"card max-w-[420px] w-full\">\n            <div class=\"card-header p-5 flex justify-center\">\n                <h2 class=\"text-xl font-semibold text-gray-900\">Sign In to EP Support System</h2>\n            </div>\n            <form wire:submit=\"login\" class=\"card-body flex flex-col gap-5 p-10\">\n                <div class=\"flex flex-col gap-1\">\n                    <label for=\"user_name\" class=\"form-label font-normal text-gray-900\">Username</label>\n                    <div class=\"input\" data-toggle-password=\"true\">\n                        <input wire:model=\"form.user_name\" id=\"user_name\" type=\"text\"\n                            placeholder=\"Enter Username\" autocomplete=\"username\" />\n                        <!--[if BLOCK]><![endif]--><!--[if ENDBLOCK]><![endif]-->\n                    </div>\n                </div>\n\n                <div class=\"flex flex-col gap-1\">\n                    <div class=\"flex items-center justify-between gap-1\">\n                        <label for=\"password\" class=\"form-label font-normal text-gray-900\">Password</label>\n                    </div>\n                    <div class=\"input\" data-toggle-password=\"true\">\n                        <input wire:model=\"form.password\" id=\"password\" type=\"password\"\n                            placeholder=\"Enter Password\" autocomplete=\"current-password\" />\n                        <!--[if BLOCK]><![endif]--><!--[if ENDBLOCK]><![endif]-->\n                        <button class=\"btn btn-icon\" data-toggle-password-trigger=\"true\" type=\"button\">\n                            <i class=\"ki-filled ki-eye text-gray-500 toggle-password-active:hidden\">\n                            </i>\n                            <i class=\"ki-filled ki-eye-slash text-gray-500 hidden toggle-password-active:block\">\n                            </i>\n                        </button>\n                    </div>\n                </div>\n\n                <label class=\"checkbox-group\">\n                    <input wire:model=\"form.remember\" type=\"checkbox\" class=\"checkbox checkbox-sm\" />\n                    <span class=\"checkbox-label\">Remember me</span>\n                </label>\n\n                <button class=\"btn btn-primary flex justify-center items-center gap-2\" type=\"submit\">\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\"\n                        stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\">\n                        <path d=\"M15 3h4a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2h-4\"></path>\n                        <polyline points=\"10 17 15 12 10 7\"></polyline>\n                        <line x1=\"15\" y1=\"12\" x2=\"3\" y2=\"12\"></line>\n                    </svg>\n                    Sign In\n                </button>\n\n                <div class=\"text-center text-gray-500 text-sm mt-2\">\n                    <p>Authentication methods supported:</p>\n                    <p class=\"mt-1\">LDAP, CRM, EPSS</p>\n                    <!--[if BLOCK]><![endif]-->                        <p class=\"mt-1\">LDAP requires pre-existing account.</p>\n                    <!--[if ENDBLOCK]><![endif]-->\n                    <p class=\"mt-1\">Having trouble signing in? Contact your system administrator.</p>\n                </div>\n            </form>\n        </div>\n    </div>\n\n    <!-- Right: Background / branding -->\n    <div\n        class=\"lg:rounded-xl lg:border lg:border-gray-200 lg:m-5 order-1 lg:order-2 bg-top xxl:bg-center xl:bg-cover bg-no-repeat branded-bg\">\n        <div class=\"flex flex-col p-8 lg:p-16 gap-4\">\n            <a href=\"#\">\n                <img class=\"h-[36px] max-w-none\" src=\"http://127.0.0.1:8000/assets/media/app/epss-logo.svg\" />\n            </a>\n            <div class=\"flex flex-col gap-3\">\n                <h3 class=\"text-2xl font-semibold text-gray-900\">EP Support System</h3>\n                <div class=\"text-base font-medium text-gray-600\">\n                    is a dedicated support and assistance platform\n                    <br />\n                    designed to enhance and streamline user experience\n                    <br />\n                    within the\n                    <span class=\"text-gray-900 font-semibold\">\n                        ePerolehan (eP) system\n                    </span>\n                </div>\n            </div>\n        </div>\n    </div>\n</div>", "layout": {"__class__": "Livewire\\Features\\SupportPageComponents\\PageComponentConfig", "slots": {"__type__": "array"}, "viewContext": {"__class__": "Livewire\\Mechanisms\\HandleComponents\\ViewContext", "slots": {"__type__": "array"}, "pushes": {"__type__": "array"}, "prepends": {"__type__": "array"}, "sections": {"__type__": "array"}}, "response": null, "type": "component", "view": "layouts.guest", "slotOrSection": "slot", "params": {"__type__": "array", "attributes": {"__class__": "Illuminate\\View\\ComponentAttributeBag", "*attributes": {"__type__": "array"}}}}}}}, {"description": "Rendering a view", "start": 1753685241.509873, "end": 1753685241.509873, "duration": 0, "color": null, "data": {"name": "layouts.guest", "data": {"__type__": "array", "attributes": {"__class__": "Illuminate\\View\\ComponentAttributeBag", "*attributes": {"__type__": "array"}}, "slot": {"__class__": "Illuminate\\View\\ComponentSlot", "attributes": {"__class__": "Illuminate\\View\\ComponentAttributeBag", "*attributes": {"__type__": "array"}}, "*contents": "<div wire:snapshot=\"{&quot;data&quot;:{&quot;form&quot;:[{&quot;user_name&quot;:&quot;&quot;,&quot;password&quot;:&quot;&quot;,&quot;remember&quot;:false},{&quot;class&quot;:&quot;App\\\\Livewire\\\\Forms\\\\LoginForm&quot;,&quot;s&quot;:&quot;form&quot;}]},&quot;memo&quot;:{&quot;id&quot;:&quot;9QpXBa7Jni6xHCrspnJs&quot;,&quot;name&quot;:&quot;login&quot;,&quot;path&quot;:&quot;login&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;children&quot;:[],&quot;scripts&quot;:[],&quot;assets&quot;:[],&quot;errors&quot;:[],&quot;locale&quot;:&quot;en&quot;},&quot;checksum&quot;:&quot;7fd3e5488cb10d61e5ef1b8aa1f4e9ffbf041ec345e3f5b5fbbbf8a4a31f46d2&quot;}\" wire:effects=\"[]\" wire:id=\"9QpXBa7Jni6xHCrspnJs\" class=\"grid lg:grid-cols-2 grow min-h-screen\">\n    <style>\n        .branded-bg {\n            background-image: url(http://127.0.0.1:8000/assets/media/images/2600x1600/1.png);\n        }\n\n        .dark .branded-bg {\n            background-image: url(http://127.0.0.1:8000/assets/media/images/2600x1600/1-dark.png);\n        }\n\n        .error-message {\n            color: rgb(239 68 68) !important;\n        }\n    </style>\n    <!-- Left: Login form container -->\n    <div class=\"flex justify-center items-center p-8 lg:p-10 order-2 lg:order-1\">\n        <div class=\"card max-w-[420px] w-full\">\n            <div class=\"card-header p-5 flex justify-center\">\n                <h2 class=\"text-xl font-semibold text-gray-900\">Sign In to EP Support System</h2>\n            </div>\n            <form wire:submit=\"login\" class=\"card-body flex flex-col gap-5 p-10\">\n                <div class=\"flex flex-col gap-1\">\n                    <label for=\"user_name\" class=\"form-label font-normal text-gray-900\">Username</label>\n                    <div class=\"input\" data-toggle-password=\"true\">\n                        <input wire:model=\"form.user_name\" id=\"user_name\" type=\"text\"\n                            placeholder=\"Enter Username\" autocomplete=\"username\" />\n                        <!--[if BLOCK]><![endif]--><!--[if ENDBLOCK]><![endif]-->\n                    </div>\n                </div>\n\n                <div class=\"flex flex-col gap-1\">\n                    <div class=\"flex items-center justify-between gap-1\">\n                        <label for=\"password\" class=\"form-label font-normal text-gray-900\">Password</label>\n                    </div>\n                    <div class=\"input\" data-toggle-password=\"true\">\n                        <input wire:model=\"form.password\" id=\"password\" type=\"password\"\n                            placeholder=\"Enter Password\" autocomplete=\"current-password\" />\n                        <!--[if BLOCK]><![endif]--><!--[if ENDBLOCK]><![endif]-->\n                        <button class=\"btn btn-icon\" data-toggle-password-trigger=\"true\" type=\"button\">\n                            <i class=\"ki-filled ki-eye text-gray-500 toggle-password-active:hidden\">\n                            </i>\n                            <i class=\"ki-filled ki-eye-slash text-gray-500 hidden toggle-password-active:block\">\n                            </i>\n                        </button>\n                    </div>\n                </div>\n\n                <label class=\"checkbox-group\">\n                    <input wire:model=\"form.remember\" type=\"checkbox\" class=\"checkbox checkbox-sm\" />\n                    <span class=\"checkbox-label\">Remember me</span>\n                </label>\n\n                <button class=\"btn btn-primary flex justify-center items-center gap-2\" type=\"submit\">\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\"\n                        stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\">\n                        <path d=\"M15 3h4a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2h-4\"></path>\n                        <polyline points=\"10 17 15 12 10 7\"></polyline>\n                        <line x1=\"15\" y1=\"12\" x2=\"3\" y2=\"12\"></line>\n                    </svg>\n                    Sign In\n                </button>\n\n                <div class=\"text-center text-gray-500 text-sm mt-2\">\n                    <p>Authentication methods supported:</p>\n                    <p class=\"mt-1\">LDAP, CRM, EPSS</p>\n                    <!--[if BLOCK]><![endif]-->                        <p class=\"mt-1\">LDAP requires pre-existing account.</p>\n                    <!--[if ENDBLOCK]><![endif]-->\n                    <p class=\"mt-1\">Having trouble signing in? Contact your system administrator.</p>\n                </div>\n            </form>\n        </div>\n    </div>\n\n    <!-- Right: Background / branding -->\n    <div\n        class=\"lg:rounded-xl lg:border lg:border-gray-200 lg:m-5 order-1 lg:order-2 bg-top xxl:bg-center xl:bg-cover bg-no-repeat branded-bg\">\n        <div class=\"flex flex-col p-8 lg:p-16 gap-4\">\n            <a href=\"#\">\n                <img class=\"h-[36px] max-w-none\" src=\"http://127.0.0.1:8000/assets/media/app/epss-logo.svg\" />\n            </a>\n            <div class=\"flex flex-col gap-3\">\n                <h3 class=\"text-2xl font-semibold text-gray-900\">EP Support System</h3>\n                <div class=\"text-base font-medium text-gray-600\">\n                    is a dedicated support and assistance platform\n                    <br />\n                    designed to enhance and streamline user experience\n                    <br />\n                    within the\n                    <span class=\"text-gray-900 font-semibold\">\n                        ePerolehan (eP) system\n                    </span>\n                </div>\n            </div>\n        </div>\n    </div>\n</div>"}}}}], "userData": [], "httpRequests": [], "subrequests": [], "xdebug": [], "commandName": null, "commandArguments": [], "commandArgumentsDefaults": [], "commandOptions": [], "commandOptionsDefaults": [], "commandExitCode": null, "commandOutput": null, "jobName": null, "jobDescription": null, "jobStatus": null, "jobPayload": [], "jobQueue": null, "jobConnection": null, "jobOptions": [], "testName": null, "testStatus": null, "testStatusMessage": null, "testAsserts": [], "clientMetrics": [], "webVitals": [], "parent": null, "updateToken": "983195a7"}