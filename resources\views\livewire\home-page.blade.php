<div class="container-fluid">
    <!-- Welcome Section -->
    <div class="card mb-5 mb-xl-8">
        <div class="card-body">
            <div class="d-flex align-items-center">
                <div class="flex-grow-1">
                    <h1 class="text-gray-900 fw-bold fs-2x mb-2">
                        Welcome back, {{ $userName }}!
                    </h1>
                    <p class="text-gray-600 fs-6 mb-0">
                        Access your authorized modules and manage your tasks efficiently.
                    </p>
                </div>
                <div class="d-none d-lg-block">
                    <i class="ki-filled ki-home text-primary" style="font-size: 4rem;"></i>
                </div>
            </div>
        </div>
    </div>

    <!-- Dashboard Navigation Cards -->
    @if(count($dashboardCards) > 0)
        <div class="row g-5 g-xl-8">
            @foreach($dashboardCards as $card)
                <div class="col-xl-4 col-lg-6 col-md-6 col-sm-12">
                    <div class="card card-flush h-xl-100 hover-elevate-up shadow-sm">
                        <div class="card-header pt-7">
                            <div class="card-title d-flex flex-column">
                                <!-- Icon -->
                                <div class="d-flex align-items-center mb-3">
                                    <div class="symbol symbol-circle symbol-50px me-3 bg-light-{{ $card['color'] }}">
                                        <div class="symbol-label">
                                            <i class="ki-filled {{ $card['icon'] }} text-{{ $card['color'] }} fs-2x"></i>
                                        </div>
                                    </div>
                                    <div class="flex-grow-1">
                                        <span class="text-gray-800 text-hover-primary fw-bold fs-4">
                                            {{ $card['title'] }}
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="card-body pt-0">
                            <!-- Description -->
                            <div class="text-gray-600 fw-semibold fs-6 mb-4">
                                {{ $card['description'] }}
                            </div>
                            <!-- Action Button -->
                            <div class="d-flex justify-content-end">
                                <a href="{{ $card['url'] }}"
                                   class="btn btn-sm btn-{{ $card['color'] }} btn-flex btn-center">
                                    <i class="ki-filled ki-arrow-right fs-7 ms-1"></i>
                                    Access Module
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            @endforeach
        </div>
    @else
        <!-- No Access Message -->
        <div class="card">
            <div class="card-body text-center py-10">
                <div class="mb-5">
                    <i class="ki-filled ki-information-2 text-muted" style="font-size: 4rem;"></i>
                </div>
                <h3 class="text-gray-800 fw-bold mb-3">No Modules Available</h3>
                <p class="text-gray-600 fs-6 mb-0">
                    You don't have access to any modules at the moment.
                    Please contact your administrator to request access.
                </p>
            </div>
        </div>
    @endif

    <!-- Quick Stats Section (Optional) -->
    <div class="row g-5 g-xl-8 mt-5">
        <div class="col-xl-12">
            <div class="card card-flush">
                <div class="card-header pt-7">
                    <h3 class="card-title align-items-start flex-column">
                        <span class="card-label fw-bold text-gray-800">Quick Information</span>
                        <span class="text-gray-500 mt-1 fw-semibold fs-6">System overview</span>
                    </h3>
                </div>
                <div class="card-body pt-6">
                    <div class="row g-5">
                        <div class="col-lg-3 col-md-6">
                            <div class="d-flex align-items-center">
                                <div class="symbol symbol-40px me-3">
                                    <div class="symbol-label bg-light-success">
                                        <i class="ki-filled ki-check text-success fs-1"></i>
                                    </div>
                                </div>
                                <div class="flex-grow-1">
                                    <span class="text-gray-800 fw-bold fs-6 d-block">System Status</span>
                                    <span class="text-success fw-semibold fs-7">Online</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-6">
                            <div class="d-flex align-items-center">
                                <div class="symbol symbol-40px me-3">
                                    <div class="symbol-label bg-light-primary">
                                        <i class="ki-filled ki-user text-primary fs-1"></i>
                                    </div>
                                </div>
                                <div class="flex-grow-1">
                                    <span class="text-gray-800 fw-bold fs-6 d-block">Your Role</span>
                                    <span class="text-primary fw-semibold fs-7">Authorized User</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-6">
                            <div class="d-flex align-items-center">
                                <div class="symbol symbol-40px me-3">
                                    <div class="symbol-label bg-light-info">
                                        <i class="ki-filled ki-calendar text-info fs-1"></i>
                                    </div>
                                </div>
                                <div class="flex-grow-1">
                                    <span class="text-gray-800 fw-bold fs-6 d-block">Last Login</span>
                                    <span class="text-info fw-semibold fs-7">{{ now()->format('M d, Y') }}</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-6">
                            <div class="d-flex align-items-center">
                                <div class="symbol symbol-40px me-3">
                                    <div class="symbol-label bg-light-warning">
                                        <i class="ki-filled ki-shield-tick text-warning fs-1"></i>
                                    </div>
                                </div>
                                <div class="flex-grow-1">
                                    <span class="text-gray-800 fw-bold fs-6 d-block">Security</span>
                                    <span class="text-warning fw-semibold fs-7">Protected</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>