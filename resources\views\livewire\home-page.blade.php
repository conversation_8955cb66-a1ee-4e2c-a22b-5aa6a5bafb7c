<!-- Custom Styles for Dashboard Cards -->
<style>
.card-clickable {
    transition: all 0.3s ease;
    cursor: pointer;
    border: 1px solid #e4e6ef;
}

.card-clickable:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1) !important;
    border-color: #3f4254;
}

.card-link {
    display: block;
    color: inherit;
}

.card-link:hover {
    color: inherit;
    text-decoration: none;
}

.card-link:hover .card-clickable {
    border-color: var(--bs-primary);
}

/* Ensure equal height cards */
.row.g-6 .col-xl-4,
.row.g-6 .col-lg-6,
.row.g-6 .col-md-6 {
    display: flex;
}

.card-link {
    flex: 1;
}
</style>

<div class="container-fluid">
    <!-- Welcome Section -->
    <div class="card mb-5 mb-xl-8">
        <div class="card-body">
            <div class="d-flex align-items-center">
                <div class="flex-grow-1">
                    <h1 class="text-gray-900 fw-bold fs-2x mb-2">
                        Welcome back, {{ $userName }}!
                    </h1>
                    <p class="text-gray-600 fs-6 mb-0">
                        Access your authorized modules and manage your tasks efficiently.
                    </p>
                </div>
                <div class="d-none d-lg-block">
                    <i class="ki-filled ki-home text-primary" style="font-size: 4rem;"></i>
                </div>
            </div>
        </div>
    </div>

    <!-- Dashboard Navigation Cards -->
    @if(count($dashboardCards) > 0)
        <div class="row g-6 g-xl-9">
            @foreach($dashboardCards as $card)
                <div class="col-xl-4 col-lg-6 col-md-6 col-sm-12">
                    <a href="{{ $card['url'] }}" class="card-link text-decoration-none">
                        <div class="card card-flush h-100 hover-elevate-up shadow-sm card-clickable">
                            <div class="card-body d-flex flex-column justify-content-between p-9">
                                <!-- Icon and Title Section -->
                                <div class="d-flex align-items-center mb-5">
                                    <div class="symbol symbol-circle symbol-60px me-4 bg-light-{{ $card['color'] }}">
                                        <div class="symbol-label">
                                            <i class="ki-filled {{ $card['icon'] }} text-{{ $card['color'] }} fs-2x"></i>
                                        </div>
                                    </div>
                                    <div class="flex-grow-1">
                                        <h3 class="text-gray-900 fw-bold fs-4 mb-0">
                                            {{ $card['title'] }}
                                        </h3>
                                    </div>
                                </div>

                                <!-- Description -->
                                <div class="text-gray-600 fw-semibold fs-6 mb-5 flex-grow-1">
                                    {{ $card['description'] }}
                                </div>

                                <!-- Access Indicator -->
                                <div class="d-flex align-items-center justify-content-between">
                                    <span class="badge badge-light-{{ $card['color'] }} fs-7 fw-bold">
                                        Click to Access
                                    </span>
                                    <i class="ki-filled ki-arrow-right text-{{ $card['color'] }} fs-2"></i>
                                </div>
                            </div>
                        </div>
                    </a>
                </div>
            @endforeach
        </div>
    @else
        <!-- No Access Message -->
        <div class="card">
            <div class="card-body text-center py-10">
                <div class="mb-5">
                    <i class="ki-filled ki-information-2 text-muted" style="font-size: 4rem;"></i>
                </div>
                <h3 class="text-gray-800 fw-bold mb-3">No Modules Available</h3>
                <p class="text-gray-600 fs-6 mb-0">
                    You don't have access to any modules at the moment.
                    Please contact your administrator to request access.
                </p>
            </div>
        </div>
    @endif

    <!-- Quick Stats Section -->
    <div class="row g-6 g-xl-9 mt-8">
        <div class="col-12">
            <div class="card card-flush">
                <div class="card-header pt-8 pb-0">
                    <h3 class="card-title align-items-start flex-column">
                        <span class="card-label fw-bold text-gray-800 fs-3">Quick Information</span>
                        <span class="text-gray-500 mt-1 fw-semibold fs-6">System overview and status</span>
                    </h3>
                </div>
                <div class="card-body pt-6">
                    <div class="row g-6">
                        <div class="col-lg-3 col-md-6 col-sm-6">
                            <div class="d-flex align-items-center p-4 bg-light-success rounded">
                                <div class="symbol symbol-50px me-4">
                                    <div class="symbol-label bg-success">
                                        <i class="ki-filled ki-check text-white fs-2x"></i>
                                    </div>
                                </div>
                                <div class="flex-grow-1">
                                    <span class="text-gray-800 fw-bold fs-5 d-block">System Status</span>
                                    <span class="text-success fw-bold fs-6">Online</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-6 col-sm-6">
                            <div class="d-flex align-items-center p-4 bg-light-primary rounded">
                                <div class="symbol symbol-50px me-4">
                                    <div class="symbol-label bg-primary">
                                        <i class="ki-filled ki-user text-white fs-2x"></i>
                                    </div>
                                </div>
                                <div class="flex-grow-1">
                                    <span class="text-gray-800 fw-bold fs-5 d-block">Your Role</span>
                                    <span class="text-primary fw-bold fs-6">Authorized User</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-6 col-sm-6">
                            <div class="d-flex align-items-center p-4 bg-light-info rounded">
                                <div class="symbol symbol-50px me-4">
                                    <div class="symbol-label bg-info">
                                        <i class="ki-filled ki-calendar text-white fs-2x"></i>
                                    </div>
                                </div>
                                <div class="flex-grow-1">
                                    <span class="text-gray-800 fw-bold fs-5 d-block">Last Login</span>
                                    <span class="text-info fw-bold fs-6">{{ now()->format('M d, Y') }}</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-6 col-sm-6">
                            <div class="d-flex align-items-center p-4 bg-light-warning rounded">
                                <div class="symbol symbol-50px me-4">
                                    <div class="symbol-label bg-warning">
                                        <i class="ki-filled ki-shield-tick text-white fs-2x"></i>
                                    </div>
                                </div>
                                <div class="flex-grow-1">
                                    <span class="text-gray-800 fw-bold fs-5 d-block">Security</span>
                                    <span class="text-warning fw-bold fs-6">Protected</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>