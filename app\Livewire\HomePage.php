<?php

namespace App\Livewire;

use Livewire\Component;
use Livewire\Attributes\Layout;
use App\Services\MenuService;
use Illuminate\Support\Facades\Auth;

#[Layout('layouts.app')] // Specify the layout file
class HomePage extends Component
{
    public $dashboardCards = [];
    public $userName = '';

    public function mount()
    {
        $this->userName = Auth::user()->first_name ?? Auth::user()->name ?? 'User';
        $this->loadDashboardCards();
    }

    protected function loadDashboardCards()
    {
        $menuService = new MenuService();
        $menuItems = $menuService->getMenu();

        $filteredCards = [];

        foreach ($menuItems as $item) {
            // Show main navigation items and sections (not hidden)
            if (($item['type'] === 'item' || $item['type'] === 'section') &&
                !($item['hidden'] ?? false) &&
                isset($item['url']) &&
                $item['id'] !== 'home') { // Exclude home from dashboard cards

                $filteredCards[] = [
                    'id' => $item['id'],
                    'title' => $item['title'],
                    'icon' => $item['icon'] ?? 'ki-element-11',
                    'url' => $item['url'],
                    'description' => $this->getCardDescription($item['id']),
                    'color' => $this->getCardColor($item['id']),
                ];
            }
        }

        $this->dashboardCards = $filteredCards;
    }

    protected function getCardDescription($cardId)
    {
        $descriptions = [
            'crm' => 'Manage customer relationships, cases, and support tickets',
            'ep' => 'Access e-Perolehan system for procurement and supplier management',
            'eperolehan' => 'Access e-Perolehan system for procurement and supplier management',
            'report' => 'View comprehensive reports and analytics',
            'bpm' => 'Business Process Management tools and workflows',
            'settings' => 'Application settings and user management',
        ];

        return $descriptions[$cardId] ?? 'Access ' . ucfirst($cardId) . ' module';
    }

    protected function getCardColor($cardId)
    {
        $colors = [
            'crm' => 'primary',
            'ep' => 'success',
            'report' => 'info',
            'bpm' => 'warning',
            'settings' => 'dark',
        ];

        return $colors[$cardId] ?? 'primary';
    }

    public function render()
    {
        return view('livewire.home-page');
    }
}