<div class="pb-5">
    <div class="container-fluid flex items-center justify-between flex-wrap gap-3">
        <div class="flex items-center flex-wrap gap-1 lg:gap-5">
            <!-- Page Title (dynamic) -->
            <h1 class="font-medium text-base text-gray-900">
                <?php echo e(isset($breadcrumbs[0]) ? $breadcrumbs[0]['title'] : 'Home'); ?>

            </h1>

            <!-- Breadcrumbs -->
            <div class="flex items-center flex-wrap gap-1 text-sm">
                <?php $__currentLoopData = $breadcrumbs; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $breadcrumb): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <!--[if BLOCK]><![endif]--><?php if($index > 0): ?>
                        <!-- Add separator before each segment (except first) -->
                        <span class="text-gray-400 text-sm">
                            /
                        </span>
                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

                    <!--[if BLOCK]><![endif]--><?php if(!$breadcrumb['is_last']): ?>
                        <!-- Middle segments -->
                        <span class="text-gray-700">
                            <?php echo e($breadcrumb['title']); ?>

                        </span>
                    <?php else: ?>
                        <!-- Last segment has different styling -->
                        <span class="text-gray-900">
                            <?php echo e($breadcrumb['title']); ?>

                        </span>
                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
            </div>
        </div>
        <div class="flex items-center flex-wrap gap-1.5 lg:gap-3.5">
            <!-- Search Button -->
            <button
                class="btn btn-icon btn-icon-lg size-9 rounded-md hover:bg-gray-200 dropdown-open:bg-gray-200 hover:text-primary text-gray-600"
                data-modal-toggle="#search_modal">
                <i class="ki-filled ki-magnifier"></i>
            </button>
            <!-- Notifications Menu -->
            <?php echo $__env->make('partials.header.notification-menu', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
            <!-- Apps Menu (optional) -->
            
            <!-- Example Export Button (optional) -->
            
        </div>
    </div>
</div><?php /**PATH E:\workspace\epss-revamp\resources\views/livewire/component/header.blade.php ENDPATH**/ ?>