<div class="dropdown" data-dropdown="true" data-dropdown-offset="-10px, 10px" data-dropdown-placement="bottom-end" data-dropdown-placement-rtl="bottom-start" data-dropdown-trigger="click|lg:click">
    <button class="dropdown-toggle btn btn-icon btn-icon-lg size-9 rounded-md hover:bg-gray-200 dropdown-open:bg-gray-200 hover:text-primary text-gray-600">
        <i class="ki-filled ki-notification-status"></i>
        <!-- Add a badge if there are unread notifications -->
        
    </button>
    <div class="dropdown-content light:border-gray-300 w-full max-w-[460px]">
        <div class="flex items-center justify-between gap-2.5 text-sm text-gray-900 font-semibold px-5 py-2.5 border-b border-b-gray-200" id="notifications_header_new">
            Notifications
            <button class="btn btn-sm btn-icon btn-light btn-clear shrink-0" data-dropdown-dismiss="true">
                <i class="ki-filled ki-cross"></i>
            </button>
        </div>
        <!-- Notification Tabs -->
        <div class="tabs justify-between px-5 mb-2" data-tabs="true" id="notifications_tabs_new">
            <div class="flex items-center gap-5">
                <button class="tab active" data-tab-toggle="#notifications_tab_all_new">All</button>
                <button class="tab relative" data-tab-toggle="#notifications_tab_inbox_new">
                    Inbox
                    <!-- Add a badge for inbox if needed -->
                    
                </button>
                <button class="tab" data-tab-toggle="#notifications_tab_team_new">Team</button>
            </div>
            <!-- Settings Menu (optional) -->
            <div class="menu" data-menu="true">
                <div class="menu-item" data-menu-item-offset="0, 10px" data-menu-item-placement="bottom-end" data-menu-item-placement-rtl="bottom-start" data-menu-item-toggle="dropdown" data-menu-item-trigger="click|lg:hover">
                    <button class="menu-toggle btn btn-sm btn-icon btn-light btn-clear"><i class="ki-filled ki-setting-2"></i></button>
                    <div class="menu-dropdown menu-default w-full max-w-[175px]" data-menu-dismiss="true">
                        <!-- Add settings menu items if needed -->
                    </div>
                </div>
            </div>
        </div>

        <!-- Tab Content Panes -->
        <div class="grow" id="notifications_tab_all_new">
            <div class="flex flex-col">
                <div class="scrollable-y-auto" data-scrollable="true" data-scrollable-dependencies="#notifications_header_new" data-scrollable-max-height="auto" data-scrollable-offset="200px">
                    <!-- Load dynamic notifications here -->
                    <div class="p-5 text-center text-gray-600">No notifications yet.</div>
                </div>
                <div class="border-b border-b-gray-200"></div>
                <div class="grid grid-cols-2 p-5 gap-2.5">
                    <button class="btn btn-sm btn-light justify-center">Archive all</button>
                    <button class="btn btn-sm btn-light justify-center">Mark all as read</button>
                </div>
            </div>
        </div>
        
        <div class="grow hidden" id="notifications_tab_inbox_new">Inbox Content...</div>
        <div class="grow hidden" id="notifications_tab_team_new">Team Content...</div>
    </div>
</div><?php /**PATH E:\workspace\epss-revamp\resources\views/partials/header/notification-menu.blade.php ENDPATH**/ ?>