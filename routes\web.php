<?php

use App\Livewire\AppSetting;
use App\Livewire\HomePage;
use App\Livewire\Login;
use App\Livewire\Project\CRM;
use App\Livewire\Project\EPerolehan;
use App\Livewire\UserProfile;
use Illuminate\Support\Facades\Route;
// use App\Http\Controllers\ExportController;
use App\Livewire\Project\Report;
use App\Livewire\Project\Bpm;

$authMiddleware = [
    'auth:web',
    'log.activity'
];

Route::get('/login', Login::class)->name('login');

// All routes inside this group will require authentication but not permission
Route::middleware([...$authMiddleware])->group(function () {
    Route::redirect('/', '/home', 301);
    Route::get('/home', HomePage::class)->name('home');
    Route::get('/profile', UserProfile::class)->name('profile');
});

// All routes inside this group will be checked against permissions in the database
Route::middleware([...$authMiddleware, 'route.permission'])->group(function () {
    Route::get('/crm', CRM::class)
        ->name('crm');

    Route::get('/ep', EPerolehan::class)
        ->name('ep');

    Route::get('/report', Report::class)
        ->name('report');

    Route::get('/bpm', Bpm::class)
        ->name('bpm');

    Route::get('/settings', AppSetting::class)
        ->name('settings');

    // Add more routes as needed...
});

// Route::post('download', [ExportController::class, 'downloadUom'])->name('download');