{"id": "1753685241-9147-2074412086", "version": 1, "type": "request", "time": 1753685241.599082, "method": "GET", "url": "http://127.0.0.1:8000/livewire/livewire.js?id=df3a17f2", "uri": "/livewire/livewire.js?id=df3a17f2", "headers": {"host": ["127.0.0.1:8000"], "connection": ["keep-alive"], "sec-ch-ua-platform": ["\"Windows\""], "user-agent": ["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"], "sec-ch-ua": ["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""], "sec-ch-ua-mobile": ["?0"], "accept": ["*/*"], "sec-fetch-site": ["same-origin"], "sec-fetch-mode": ["no-cors"], "sec-fetch-dest": ["script"], "referer": ["http://127.0.0.1:8000/login"], "accept-encoding": ["gzip, deflate, br, zstd"], "accept-language": ["en-GB,en-US;q=0.9,en;q=0.8,ms;q=0.7"], "cookie": ["remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Ik52M3VabWhoUVowcmdKT3k4REhyZnc9PSIsInZhbHVlIjoiN29uVEJUdFRMVTBHdmdTa2J1U1FYODRuWmJFZGpUSkc3Z2ZDWDl0SUpZUGowbTAyaDR6MDlZTldqY2NpOUJhN3Y5RVVlK2ZLRHAyK3ZxQjhudDdvT2h5NjhDTWYycmRnb3RCYlZ1S1JuZThrY0RHaGxPdzlIN21EZTAraENhaXQ3NlMxbnNmRSt4d1diQThZQVh4OU85MlFrSkoyR045K1FOc3oweUM5SlgrVEFZc0h3a0YrR0dpZ0dTcWJQVVRteU9yamwvWldsNm41WFpZVDUvbHg5UFhvbXhRamcrUXFSckgvRHQzVGE4MD0iLCJtYWMiOiI4MzE4MWVkODlkMGI2ZGVlM2U1OTllMTJhMjE1ZjJiNDdmMjFlZTcyYmNkMWM0NjU2YTU4YTU4OTUyNDc5NDU4IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IlFHUDlqV21lVi9HQlRwNDE3K1AzV3c9PSIsInZhbHVlIjoiQXZpZHc1eHZwa01oNHpjcHppTy9PWjBnMFFlSVdORTNrTkU1ZWFOQU04ZU5PN1dycWhvc1RhYytuREY5NndmRjJ2RjVBQ2tUWjZpRFZLTkxubzRVVGgvRnZGdWs2QjYrcDZYMEwydXhsUzdETnN3ZnpaNXZCTG80c3FwdTBtMmciLCJtYWMiOiI4YzY1YWRjNmYwNDE1MzdhY2I3OWIzMjgxOWYyNTRkMGVkY2Y2ODJkOWY0YzNhNGQxZDE1MzIwMTRlOGNjYTBkIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6Ijh6TEtxK1o0MFpzL2QwWWpqb0tnRUE9PSIsInZhbHVlIjoiSm1wWU5veDFKYlZXNm4wZTNRZWUwVnc1dnBVSzMxV2lPRFpwT0NoczVaN1NUR1NIQkVwWnNvRDA4aDFpWEFYSy9SYWp5UjlVVmVrZk9HNHdLT29USjc4MW85V2pLTFpDYkNoaUpuNHZSSnRuMC96RnpPUTJBcndVZ1lkSmVMNjgiLCJtYWMiOiI4OWIxNzc5ZTg4MjNmMDBmNzhkZDlhNzIxMmRlZDgyNWQxMzYzZmRhYTNiNTI3YjIxYzY3ODgxZmI0Yzk1ZTI0IiwidGFnIjoiIn0%3D; x-clockwork=%7B%22requestId%22%3A%221753685241-2221-1712813833%22%2C%22version%22%3A%225.3.4%22%2C%22path%22%3A%22%5C%2F__clockwork%5C%2F%22%2C%22webPath%22%3A%22%5C%2Fclockwork%5C%2Fapp%22%2C%22token%22%3A%22983195a7%22%2C%22metrics%22%3Atrue%2C%22toolbar%22%3Atrue%7D"]}, "controller": "Livewire\\Mechanisms\\FrontendAssets\\FrontendAssets@returnJavaScriptAsFile", "getData": {"id": "df3a17f2"}, "postData": [], "requestData": "", "sessionData": [], "authenticatedUser": null, "cookies": {"remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "eyJpdiI6Ik52M3VabWhoUVowcmdKT3k4REhyZnc9PSIsInZhbHVlIjoiN29uVEJUdFRMVTBHdmdTa2J1U1FYODRuWmJFZGpUSkc3Z2ZDWDl0SUpZUGowbTAyaDR6MDlZTldqY2NpOUJhN3Y5RVVlK2ZLRHAyK3ZxQjhudDdvT2h5NjhDTWYycmRnb3RCYlZ1S1JuZThrY0RHaGxPdzlIN21EZTAraENhaXQ3NlMxbnNmRSt4d1diQThZQVh4OU85MlFrSkoyR045K1FOc3oweUM5SlgrVEFZc0h3a0YrR0dpZ0dTcWJQVVRteU9yamwvWldsNm41WFpZVDUvbHg5UFhvbXhRamcrUXFSckgvRHQzVGE4MD0iLCJtYWMiOiI4MzE4MWVkODlkMGI2ZGVlM2U1OTllMTJhMjE1ZjJiNDdmMjFlZTcyYmNkMWM0NjU2YTU4YTU4OTUyNDc5NDU4IiwidGFnIjoiIn0=", "XSRF-TOKEN": "eyJpdiI6IlFHUDlqV21lVi9HQlRwNDE3K1AzV3c9PSIsInZhbHVlIjoiQXZpZHc1eHZwa01oNHpjcHppTy9PWjBnMFFlSVdORTNrTkU1ZWFOQU04ZU5PN1dycWhvc1RhYytuREY5NndmRjJ2RjVBQ2tUWjZpRFZLTkxubzRVVGgvRnZGdWs2QjYrcDZYMEwydXhsUzdETnN3ZnpaNXZCTG80c3FwdTBtMmciLCJtYWMiOiI4YzY1YWRjNmYwNDE1MzdhY2I3OWIzMjgxOWYyNTRkMGVkY2Y2ODJkOWY0YzNhNGQxZDE1MzIwMTRlOGNjYTBkIiwidGFnIjoiIn0=", "laravel_session": "eyJpdiI6Ijh6TEtxK1o0MFpzL2QwWWpqb0tnRUE9PSIsInZhbHVlIjoiSm1wWU5veDFKYlZXNm4wZTNRZWUwVnc1dnBVSzMxV2lPRFpwT0NoczVaN1NUR1NIQkVwWnNvRDA4aDFpWEFYSy9SYWp5UjlVVmVrZk9HNHdLT29USjc4MW85V2pLTFpDYkNoaUpuNHZSSnRuMC96RnpPUTJBcndVZ1lkSmVMNjgiLCJtYWMiOiI4OWIxNzc5ZTg4MjNmMDBmNzhkZDlhNzIxMmRlZDgyNWQxMzYzZmRhYTNiNTI3YjIxYzY3ODgxZmI0Yzk1ZTI0IiwidGFnIjoiIn0=", "x-clockwork": "{\"requestId\":\"1753685241-2221-1712813833\",\"version\":\"5.3.4\",\"path\":\"\\/__clockwork\\/\",\"webPath\":\"\\/clockwork\\/app\",\"token\":\"983195a7\",\"metrics\":true,\"toolbar\":true}"}, "responseTime": 1753685242.264047, "responseStatus": 200, "responseDuration": 664.9649143218994, "memoryUsage": 27262976, "middleware": [], "databaseQueries": [], "databaseQueriesCount": 0, "databaseSlowQueries": 0, "databaseSelects": 0, "databaseInserts": 0, "databaseUpdates": 0, "databaseDeletes": 0, "databaseOthers": 0, "databaseDuration": 0, "cacheQueries": [], "cacheReads": 0, "cacheHits": 0, "cacheWrites": 0, "cacheDeletes": 0, "cacheTime": 0, "modelsActions": [], "modelsRetrieved": [], "modelsCreated": [], "modelsUpdated": [], "modelsDeleted": [], "redisCommands": [], "queueJobs": [], "timelineData": [{"description": "Controller", "start": 1753685242.199482, "end": 1753685242.263959, "duration": 64.47696685791016, "color": null, "data": null}], "log": [], "events": [], "routes": [{"method": "GET, HEAD", "uri": "login", "name": "login", "action": "App\\Livewire\\Login", "middleware": ["web"], "before": "", "after": ""}, {"method": "GET, HEAD", "uri": "home", "name": "home", "action": "App\\Livewire\\HomePage", "middleware": ["web", "auth:web", "log.activity"], "before": "", "after": ""}, {"method": "GET, HEAD", "uri": "profile", "name": "profile", "action": "App\\Livewire\\UserProfile", "middleware": ["web", "auth:web", "log.activity"], "before": "", "after": ""}, {"method": "GET, HEAD", "uri": "crm", "name": "crm", "action": "App\\Livewire\\Project\\CRM", "middleware": ["web", "auth:web", "log.activity", "route.permission"], "before": "", "after": ""}, {"method": "GET, HEAD", "uri": "ep", "name": "ep", "action": "App\\Livewire\\Project\\EPerolehan", "middleware": ["web", "auth:web", "log.activity", "route.permission"], "before": "", "after": ""}, {"method": "GET, HEAD", "uri": "report", "name": "report", "action": "App\\Livewire\\Project\\Report", "middleware": ["web", "auth:web", "log.activity", "route.permission"], "before": "", "after": ""}, {"method": "GET, HEAD", "uri": "bpm", "name": "bpm", "action": "App\\Livewire\\Project\\Bpm", "middleware": ["web", "auth:web", "log.activity", "route.permission"], "before": "", "after": ""}, {"method": "GET, HEAD", "uri": "settings", "name": "settings", "action": "App\\Livewire\\AppSetting", "middleware": ["web", "auth:web", "log.activity", "route.permission"], "before": "", "after": ""}], "notifications": [], "emailsData": [], "viewsData": [], "userData": [], "httpRequests": [], "subrequests": [], "xdebug": [], "commandName": null, "commandArguments": [], "commandArgumentsDefaults": [], "commandOptions": [], "commandOptionsDefaults": [], "commandExitCode": null, "commandOutput": null, "jobName": null, "jobDescription": null, "jobStatus": null, "jobPayload": [], "jobQueue": null, "jobConnection": null, "jobOptions": [], "testName": null, "testStatus": null, "testStatusMessage": null, "testAsserts": [], "clientMetrics": [], "webVitals": [], "parent": null, "updateToken": "cbfe4af1"}