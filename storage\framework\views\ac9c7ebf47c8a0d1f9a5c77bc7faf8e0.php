<?php
    // Set default level if not passed
    $level = $level ?? 1;
    // Calculate padding based on level
    $linkPadding = 'ps-' . (2.5 + ($level - 1) * 4);

    // Apply different styles based on nesting level
    $levelClasses = '';
    if ($level > 1) {
        $levelClasses = 'ms-' . (($level - 1) * 2) . ' border-s border-s-gray-200 ps-3';
    }
?>

<div class="menu-item <?php echo e($levelClasses); ?>">
    <a class="<?php echo \Illuminate\Support\Arr::toCssClasses([
        'menu-link py-2 ' . $linkPadding . ' pe-2.5 rounded-md border border-transparent',
        'menu-item-active:border-gray-200 menu-item-active:bg-light menu-link-hover:bg-light menu-link-hover:border-gray-200',
        'opacity-50 pointer-events-none' => $disabled ?? false,
        'menu-item-active border-gray-200 bg-light' => $active ?? false,
    ]); ?>" href="<?php echo e($disabled ?? false ? '#' : ($url ?? '#')); ?>"
        tabindex="<?php echo e($disabled ?? false ? '-1' : '0'); ?>">
        <!--[if BLOCK]><![endif]--><?php if($level > 1): ?>
            <span class="menu-bullet me-2">
                <span class="bullet bullet-dot size-2 bg-gray-400"></span>
            </span>
        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
        <span class="<?php echo \Illuminate\Support\Arr::toCssClasses([
            'menu-title text-2sm',
            'text-gray-800 menu-item-active:font-medium menu-item-active:text-primary menu-link-hover:text-primary' => !($disabled ?? false),
            'text-gray-400' => $disabled ?? false,
            'font-medium text-primary' => $active ?? false,
        ]); ?>">
            <?php echo e($title); ?>

        </span>
        <!--[if BLOCK]><![endif]--><?php if(isset($badge)): ?>
            <span class="menu-badge">
                <span
                    class="badge badge-xs badge-<?php echo e($badge['color'] ?? 'light'); ?> <?php echo e(isset($badge['color']) ? 'badge-outline' : ''); ?>">
                    <?php echo e($badge['text']); ?>

                </span>
            </span>
        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
    </a>
</div><?php /**PATH E:\workspace\epss-revamp\resources\views/partials/sidebar/simple-item.blade.php ENDPATH**/ ?>