<div class="fixed top-0 bottom-0 z-20 hidden lg:flex items-stretch shrink-0 w-[--tw-sidebar-width] bg-[--tw-page-bg] dark:bg-[--tw-page-bg-dark] sidebar-container"
    data-drawer="true" data-drawer-class="drawer drawer-start flex top-0 bottom-0" data-drawer-enable="true|lg:false"
    id="sidebar">

    <div class="flex flex-col items-stretch shrink-0 gap-5 py-5 w-[70px] border-e border-e-gray-300 dark:border-e-gray-200"
        id="sidebar_primary">
        <?php echo $__env->make('partials.sidebar.header', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

        
        <div class="flex grow shrink-0" id="sidebar_primary_content">
            <div class="scrollable-y-hover grow gap-2.5 shrink-0 flex ps-4 flex-col" data-scrollable="true"
                data-scrollable-dependencies="#sidebar_primary_header,#sidebar_primary_footer"
                data-scrollable-height="auto" data-scrollable-offset="80px"
                data-scrollable-wrappers="#sidebar_primary_content">

                
                <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $menuItems; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $menuItem): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <!--[if BLOCK]><![endif]--><?php if($menuItem['type'] !== 'heading' && !isset($menuItem['parent']) && !($menuItem['hidden'] ?? false)): ?>
                        <a class="btn btn-icon btn-icon-xl rounded-md size-9 border border-transparent text-gray-600 hover:bg-light hover:text-primary hover:border-gray-200 <?php echo e(($menuItem['active'] ?? false) ? 'active' : ''); ?>"
                            data-tooltip="" data-tooltip-placement="right" href="<?php echo e($menuItem['url'] ?? '#'); ?>"
                            title="<?php echo e($menuItem['title']); ?>">
                            <span class="menu-icon">
                                <i class="ki-filled <?php echo e($menuItem['icon'] ?? 'ki-question-circle'); ?>"></i>
                            </span>
                        </a>
                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->

            </div>
        </div>
        

        
        <div class="flex flex-col gap-5 items-center shrink-0" id="sidebar_primary_footer">
            
            <div class="flex flex-col gap-1.5">
                
                <button onclick="toggleSecondarySidebar()"
                    class="btn btn-icon btn-icon-xl rounded-md size-9 border border-transparent text-gray-600 hover:bg-light hover:text-primary hover:border-gray-200"
                    data-tooltip="" data-tooltip-placement="right" title="Toggle Menu">
                    <span class="menu-icon">
                        <i class="ki-filled ki-black-left-line transition-transform duration-300 ease-in-out"
                            id="sidebar-toggle-icon"></i>
                    </span>
                </button>

                
                <div class="dropdown" data-dropdown="true" data-dropdown-offset="-10px, 15px"
                    data-dropdown-placement="right-end" data-dropdown-trigger="click|lg:click">
                    <button
                        class="dropdown-toggle btn btn-icon btn-icon-xl relative rounded-md size-9 border border-transparent hover:bg-light hover:text-primary hover:border-gray-200 dropdown-open:bg-gray-200 text-gray-600">
                        <span class="menu-icon">
                            <i class="ki-filled ki-setting-2"></i>
                        </span>
                    </button>
                    <!-- Apps Dropdown Content -->
                    <div class="dropdown-content light:border-gray-300 w-screen max-w-[320px]">
                        <div
                            class="flex items-center justify-between gap-2.5 text-2xs text-gray-600 font-medium px-5 py-3 border-b border-b-gray-200">
                            <span>Apps</span>
                            <span>Enabled</span>
                        </div>
                        <div class="flex flex-col scrollable-y-auto max-h-[400px] divide-y divide-gray-200">
                            <!-- Example App Item -->
                            <div class="flex items-center justify-between flex-wrap gap-2 px-5 py-3.5">
                                <div class="flex items-center flex-wrap gap-2">
                                    <div
                                        class="flex items-center justify-center shrink-0 rounded-full bg-gray-100 border border-gray-200 size-10">
                                        <img alt="Jira logo" class="size-6"
                                            src="<?php echo e(asset('assets/media/brand-logos/jira.svg')); ?>" />
                                    </div>
                                    <div class="flex flex-col">
                                        <a class="text-2sm font-semibold text-gray-900 hover:text-primary-active"
                                            href="#">
                                            Jira
                                        </a>
                                        <span class="text-2xs font-medium text-gray-600">
                                            Project management
                                        </span>
                                    </div>
                                </div>
                                <div class="flex items-center gap-2 lg:gap-5">
                                    <label class="switch switch-sm"><input type="checkbox" value="2" /></label>
                                </div>
                            </div>
                            <div class="flex items-center justify-between flex-wrap gap-2 px-5 py-3.5">
                                <div class="flex items-center flex-wrap gap-2">
                                    <div
                                        class="flex items-center justify-center shrink-0 rounded-full bg-gray-100 border border-gray-200 size-10">
                                        <img alt="" class="size-6"
                                            src="<?php echo e(asset('assets/media/brand-logos/gitlab.svg')); ?>" />
                                    </div>
                                    <div class="flex flex-col">
                                        <a class="text-2sm font-semibold text-gray-900 hover:text-primary-active"
                                            href="#">
                                            EPSS
                                        </a>
                                        <span class="text-2xs font-medium text-gray-600">
                                            Support System
                                        </span>
                                    </div>
                                </div>
                                <div class="flex items-center gap-2 lg:gap-5">
                                    <label class="switch switch-sm"><input checked="" type="checkbox"
                                            value="1" /></label>
                                </div>
                            </div>
                        </div>
                        <div class="grid p-5 border-t border-t-gray-200">
                            <a class="btn btn-sm btn-light justify-center" href="#">
                                Go to Apps
                            </a>
                        </div>
                    </div>
                </div>
                
            </div>
            

            
            <div class="menu" data-menu="true">
                <div class="menu-item" data-menu-item-offset="-10px, 15px" data-menu-item-placement="right-end"
                    data-menu-item-toggle="dropdown" data-menu-item-trigger="click|lg:click">
                    <div class="menu-toggle btn btn-icon rounded-full">
                        <img alt="" class="size-8 rounded-full justify-center border border-gray-500 shrink-0"
                            src="<?php echo e($user['profile_photo_url'] ?? asset('assets/media/avatars/1024-1.png')); ?>">
                    </div>
                    <?php echo $__env->make('partials.sidebar.user-menu', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                </div>
            </div>
            
        </div>
        
    </div>

    
    <div class="flex items-stretch grow shrink-0 justify-center ps-1.5 my-5 me-1.5 sidebar-secondary"
        id="sidebar_secondary">
        <div class="scrollable-y-auto grow" data-scrollable="true" data-scrollable-height="auto"
            data-scrollable-offset="0px" data-scrollable-wrappers="#sidebar_secondary">
            <div class="menu flex flex-col w-full gap-px px-2.5" data-menu="true" data-menu-accordion-expand-all="false"
                id="sidebar_menu">

                
                <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $secondaryMenuItems; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $menuItem): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <!--[if BLOCK]><![endif]--><?php if($menuItem['type'] === 'heading'): ?>
                        <div class="menu-heading px-2.5 py-2 text-xs uppercase"><?php echo e($menuItem['title']); ?></div>
                    <?php elseif(isset($menuItem['items']) && count($menuItem['items']) > 0): ?>
                        
                        <?php echo $__env->make('partials.sidebar.item', $menuItem, array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                    <?php else: ?>
                        
                        <?php echo $__env->make('partials.sidebar.simple-item', $menuItem, array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
            </div>
        </div>
    </div>

    <style>
        .sidebar-container {
            transition: width 0.3s ease-in-out;
        }

        .sidebar-collapsed {
            width: var(--tw-sidebar-collapsed-width) !important;
        }

        .sidebar-collapsed .sidebar-secondary {
            display: none !important;
        }

        #sidebar_primary {
            width: 70px !important;
            min-width: 70px !important;
            max-width: 70px !important;
        }

        @media (max-width: 1024px) {
            .sidebar-collapsed {
                width: var(--tw-sidebar-width) !important;
            }

            .sidebar-collapsed .sidebar-secondary {
                display: flex !important;
            }
        }
    </style>
</div><?php /**PATH E:\workspace\epss-revamp\resources\views/livewire/component/sidebar.blade.php ENDPATH**/ ?>