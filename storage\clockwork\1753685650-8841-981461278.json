{"id": "1753685650-8841-981461278", "version": 1, "type": "request", "time": 1753685650.592061, "method": "GET", "url": "http://localhost:8000/crm", "uri": "/crm", "headers": {"host": ["localhost:8000"], "connection": ["keep-alive"], "sec-ch-ua": ["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\""], "sec-ch-ua-mobile": ["?0"], "sec-ch-ua-platform": ["\"Windows\""], "upgrade-insecure-requests": ["1"], "user-agent": ["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"], "accept": ["text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7"], "sec-fetch-site": ["same-origin"], "sec-fetch-mode": ["navigate"], "sec-fetch-user": ["?1"], "sec-fetch-dest": ["document"], "referer": ["http://localhost:8000/home"], "accept-encoding": ["gzip, deflate, br, zstd"], "accept-language": ["en-GB,en-US;q=0.9,en;q=0.8,ms;q=0.7"], "cookie": ["_ga=GA1.1.913056526.**********; Cases_divs=a%3Dnone%23h%3D%23n%3D%23; ck_login_id_20=1; ck_login_language_20=en_us; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IjNNWXlvcTJIS2ltbUV4Q1BHcDUvcHc9PSIsInZhbHVlIjoiNmo5eDlXem1zbXpZdkVMOUtoemcwd1dxQm1DN0NSWDFFK253WkZKby9OZVEweXp1b2lKclN3cUZmM0w1a1JFd01sQS85Z1NGTTRjcE9WdHNjSDVSR0pWRzh0anZQNlhPZ1VjMStjeHhidFA4dFl1Tk04clBXY1g1ekJiZFQ2Yi9EejVrVld0dklHZ0dhMTZtM042T0tBYnI0dFRGamMzRjdrQ2FMYnhSSlU2WVB3OG54dkg3dDRIckExWnk4RnI5T2h1RnU3ZktPVG5RK2lMZTk0Z3R4TVZCNUhrcHRPaHJXSk5KeUJaUDVsND0iLCJtYWMiOiIxZDAxY2E3YzQ4NWJlM2QxNDBkYTI2MmNmYzkwYzY3ZjBlNjhhYjVjNjgxNmYwMGU2OTgxNDQyNDIzZTYwMjA1IiwidGFnIjoiIn0%3D; _ga_8PH6FM2JEL=GS2.1.s1751619462$o19$g1$t1751620652$j60$l0$h0; wp-settings-1=libraryContent%3Dbrowse%26uploader%3D1%26editor%3Dhtml; wp-settings-time-1=**********; vuexy-color-scheme=dark; eP-POMS-color-scheme=dark; sugar_user_theme=SuiteP; XSRF-TOKEN=eyJpdiI6InBLekJGRjNnUXo5VHl0bE5VdHRaL3c9PSIsInZhbHVlIjoiWnlNbkJ2VVhRYkgvZDlHRW5pVm03Rm1Tc2FEcjBBTUJva0F0MU53Zm9rYmVSK283eVNxNytXRGxtU0tORHgxKzVTNkFSdlNlSmVYTnpCV054cXNBTGs4eHluaEZDa2JyaE9HdFJHakEvSE8vK2M1T1VmcVdkQVMzYkRDcHVIZDYiLCJtYWMiOiJlZmNhYzYyNzFhN2M0ZDM2YTM3MDZlMmViODFjNzFmOWNiZjk2NjlhN2Y3MzFlMjliNDMzZmJkZGM1ZmY1M2Y0IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IjJWQVB6VFh2RnRBcEM5Z3hLc0xHWVE9PSIsInZhbHVlIjoiZXBwTERNakZJYVNLamRaNjRpQlNaUjNjYnVWOUdhU28wTGpwSXJxcGZXbU1PZERlZGsrM0xwbGRPTGFZM01NVkdZTUxsNmpTalZZcWhiUVNtb1JoSXQ5bXJ1UkoyRlBCd3lZM0daZCtBN01WbEI1T21UNVdoUEI3VUoxY0x0RlIiLCJtYWMiOiI4ZWM2Mjg0NDUyYzUxNDJlM2UxNzYwMTA2NmQwOWI3M2VlNTcwNTVmZTA3NjgwODQxYTIyZWUyMWY0NTQ5NTZjIiwidGFnIjoiIn0%3D"]}, "controller": "App\\Livewire\\Project\\CRM", "getData": [], "postData": [], "requestData": "", "sessionData": {"_token": "p3mrve0pxb3fCNEYKJeVwRo8LV7TCYIotPo3DxrT", "_flash": {"__type__": "array", "old": {"__type__": "array"}, "new": {"__type__": "array"}}, "_previous": {"__type__": "array", "url": "http://localhost:8000/crm"}, "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": 1}, "authenticatedUser": {"id": 1, "username": "<EMAIL>", "email": "<EMAIL>", "name": "Super Admin"}, "cookies": {"_ga": "GA1.1.913056526.**********", "Cases_divs": "a=none#h=#n=#", "ck_login_id_20": "1", "ck_login_language_20": "en_us", "remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "eyJpdiI6IjNNWXlvcTJIS2ltbUV4Q1BHcDUvcHc9PSIsInZhbHVlIjoiNmo5eDlXem1zbXpZdkVMOUtoemcwd1dxQm1DN0NSWDFFK253WkZKby9OZVEweXp1b2lKclN3cUZmM0w1a1JFd01sQS85Z1NGTTRjcE9WdHNjSDVSR0pWRzh0anZQNlhPZ1VjMStjeHhidFA4dFl1Tk04clBXY1g1ekJiZFQ2Yi9EejVrVld0dklHZ0dhMTZtM042T0tBYnI0dFRGamMzRjdrQ2FMYnhSSlU2WVB3OG54dkg3dDRIckExWnk4RnI5T2h1RnU3ZktPVG5RK2lMZTk0Z3R4TVZCNUhrcHRPaHJXSk5KeUJaUDVsND0iLCJtYWMiOiIxZDAxY2E3YzQ4NWJlM2QxNDBkYTI2MmNmYzkwYzY3ZjBlNjhhYjVjNjgxNmYwMGU2OTgxNDQyNDIzZTYwMjA1IiwidGFnIjoiIn0=", "_ga_8PH6FM2JEL": "GS2.1.s1751619462$o19$g1$t1751620652$j60$l0$h0", "wp-settings-1": "libraryContent=browse&uploader=1&editor=html", "wp-settings-time-1": "**********", "vuexy-color-scheme": "dark", "eP-POMS-color-scheme": "dark", "sugar_user_theme": "SuiteP", "XSRF-TOKEN": "eyJpdiI6InBLekJGRjNnUXo5VHl0bE5VdHRaL3c9PSIsInZhbHVlIjoiWnlNbkJ2VVhRYkgvZDlHRW5pVm03Rm1Tc2FEcjBBTUJva0F0MU53Zm9rYmVSK283eVNxNytXRGxtU0tORHgxKzVTNkFSdlNlSmVYTnpCV054cXNBTGs4eHluaEZDa2JyaE9HdFJHakEvSE8vK2M1T1VmcVdkQVMzYkRDcHVIZDYiLCJtYWMiOiJlZmNhYzYyNzFhN2M0ZDM2YTM3MDZlMmViODFjNzFmOWNiZjk2NjlhN2Y3MzFlMjliNDMzZmJkZGM1ZmY1M2Y0IiwidGFnIjoiIn0=", "laravel_session": "eyJpdiI6IjJWQVB6VFh2RnRBcEM5Z3hLc0xHWVE9PSIsInZhbHVlIjoiZXBwTERNakZJYVNLamRaNjRpQlNaUjNjYnVWOUdhU28wTGpwSXJxcGZXbU1PZERlZGsrM0xwbGRPTGFZM01NVkdZTUxsNmpTalZZcWhiUVNtb1JoSXQ5bXJ1UkoyRlBCd3lZM0daZCtBN01WbEI1T21UNVdoUEI3VUoxY0x0RlIiLCJtYWMiOiI4ZWM2Mjg0NDUyYzUxNDJlM2UxNzYwMTA2NmQwOWI3M2VlNTcwNTVmZTA3NjgwODQxYTIyZWUyMWY0NTQ5NTZjIiwidGFnIjoiIn0="}, "responseTime": 1753685652.050692, "responseStatus": 200, "responseDuration": 1458.6310386657715, "memoryUsage": 31457280, "middleware": ["web", "auth:web", "log.activity", "route.permission"], "databaseQueries": [{"query": "SELECT * FROM `sessions` WHERE `id` = 'OedfnZ6nkElQ8MPwP2x8fU0cPiU7786JmxmSwmTv' LIMIT 1", "duration": 51.64, "connection": "epss2", "time": 1753685651.120655, "trace": [{"call": "Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():167}()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware.php", "line": 19, "isVendor": true}, {"call": "Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php", "line": 21, "isVendor": true}, {"call": "Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php", "line": 31, "isVendor": true}, {"call": "Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php", "line": 21, "isVendor": true}, {"call": "Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php", "line": 51, "isVendor": true}, {"call": "Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php", "line": 27, "isVendor": true}, {"call": "Illuminate\\Http\\Middleware\\ValidatePostSize->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `users` WHERE `id` = 1 and `users`.`deleted_at` IS NULL LIMIT 1", "duration": 8.87, "connection": "epss2", "time": 1753685651.2008662, "trace": [{"call": "Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():167}()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware.php", "line": 19, "isVendor": true}, {"call": "Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php", "line": 21, "isVendor": true}, {"call": "Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php", "line": 31, "isVendor": true}, {"call": "Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php", "line": 21, "isVendor": true}, {"call": "Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php", "line": 51, "isVendor": true}, {"call": "Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php", "line": 27, "isVendor": true}, {"call": "Illuminate\\Http\\Middleware\\ValidatePostSize->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208, "isVendor": true}], "model": "App\\Models\\User", "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_1_route_crm_action_read')", "duration": 9.69, "connection": "epss2", "time": 1753685651.2248569, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 161, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Http\\Middleware\\CheckRoutePermission.php", "line": 39, "isVendor": false}, {"call": "App\\Http\\Middleware\\CheckRoutePermission->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()", "file": "E:\\workspace\\epss-revamp\\app\\Http\\Middleware\\LogUserActivity.php", "line": 26, "isVendor": false}, {"call": "App\\Http\\Middleware\\LogUserActivity->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50, "isVendor": true}, {"call": "Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 63, "isVendor": true}, {"call": "Illuminate\\Auth\\Middleware\\Authenticate->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php", "line": 87, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_1_route_crm_action_read')", "duration": 8.57, "connection": "epss2", "time": 1753685651.236, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Http\\Middleware\\CheckRoutePermission.php", "line": 39, "isVendor": false}, {"call": "App\\Http\\Middleware\\CheckRoutePermission->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()", "file": "E:\\workspace\\epss-revamp\\app\\Http\\Middleware\\LogUserActivity.php", "line": 26, "isVendor": false}, {"call": "App\\Http\\Middleware\\LogUserActivity->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50, "isVendor": true}, {"call": "Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 63, "isVendor": true}, {"call": "Illuminate\\Auth\\Middleware\\Authenticate->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php", "line": 87, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_1_route_crm_action_read')", "duration": 8.36, "connection": "epss2", "time": 1753685651.273606, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 161, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 32, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}, {"call": "Livewire\\Wrapped->__call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Features\\SupportLifecycleHooks\\SupportLifecycleHooks.php", "line": 134, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_1_route_crm_action_read')", "duration": 11.72, "connection": "epss2", "time": 1753685651.283387, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 32, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}, {"call": "Livewire\\Wrapped->__call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Features\\SupportLifecycleHooks\\SupportLifecycleHooks.php", "line": 134, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_1_route_crm.cs-cases_action_read')", "duration": 9.68, "connection": "epss2", "time": 1753685651.2970839, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 161, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 32, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_1_route_crm.cs-cases_action_read')", "duration": 8.46, "connection": "epss2", "time": 1753685651.308676, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 32, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_1_route_crm.cs-pending-input_action_read')", "duration": 8.37, "connection": "epss2", "time": 1753685651.318717, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 161, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 32, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_1_route_crm.cs-pending-input_action_read')", "duration": 8.82, "connection": "epss2", "time": 1753685651.327936, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 32, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_1_route_crm.poms-talkdesk_action_read')", "duration": 8.7, "connection": "epss2", "time": 1753685651.3378391, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 161, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 32, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_1_route_crm.poms-talkdesk_action_read')", "duration": 7.91, "connection": "epss2", "time": 1753685651.347414, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 32, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_1_route_ep_action_read')", "duration": 8.09, "connection": "epss2", "time": 1753685651.35637, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 161, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 32, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}, {"call": "Livewire\\Wrapped->__call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Features\\SupportLifecycleHooks\\SupportLifecycleHooks.php", "line": 134, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_1_route_ep_action_read')", "duration": 9.24, "connection": "epss2", "time": 1753685651.365112, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 32, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}, {"call": "Livewire\\Wrapped->__call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Features\\SupportLifecycleHooks\\SupportLifecycleHooks.php", "line": 134, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_1_route_ep.qt.dashboard_action_read')", "duration": 9.32, "connection": "epss2", "time": 1753685651.376357, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 161, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 32, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_1_route_ep.qt.dashboard_action_read')", "duration": 12.85, "connection": "epss2", "time": 1753685651.387625, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 32, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_1_route_ep.qt.summary_action_read')", "duration": 9.4, "connection": "epss2", "time": 1753685651.402584, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 161, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 32, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_1_route_ep.qt.summary_action_read')", "duration": 8.92, "connection": "epss2", "time": 1753685651.413659, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 32, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_1_route_ep.profile.my-identity_action_read')", "duration": 8.92, "connection": "epss2", "time": 1753685651.425318, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 161, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 32, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_1_route_ep.profile.my-identity_action_read')", "duration": 9.47, "connection": "epss2", "time": 1753685651.435465, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 32, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_1_route_ep.profile.user-login_action_read')", "duration": 9.03, "connection": "epss2", "time": 1753685651.4477139, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 161, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 32, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_1_route_ep.profile.user-login_action_read')", "duration": 9.07, "connection": "epss2", "time": 1753685651.459146, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 32, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_1_route_ep.profile.organization_action_read')", "duration": 9.35, "connection": "epss2", "time": 1753685651.470496, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 161, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 32, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_1_route_ep.profile.organization_action_read')", "duration": 14, "connection": "epss2", "time": 1753685651.481307, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 32, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_1_route_ep.supplier-technical_action_read')", "duration": 8.84, "connection": "epss2", "time": 1753685651.497309, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 161, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 32, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_1_route_ep.supplier-technical_action_read')", "duration": 10.07, "connection": "epss2", "time": 1753685651.508462, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 32, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_1_route_ep.supplier-cs_action_read')", "duration": 9.87, "connection": "epss2", "time": 1753685651.521128, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 161, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 32, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_1_route_ep.supplier-cs_action_read')", "duration": 9.57, "connection": "epss2", "time": 1753685651.533277, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 32, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_1_route_ep.tracking-diary_action_read')", "duration": 8.4, "connection": "epss2", "time": 1753685651.545428, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 161, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 32, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_1_route_ep.tracking-diary_action_read')", "duration": 8.22, "connection": "epss2", "time": 1753685651.554965, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 32, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_1_route_ep.item.uom_action_read')", "duration": 8.67, "connection": "epss2", "time": 1753685651.565081, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 161, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 32, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_1_route_ep.item.uom_action_read')", "duration": 8.8, "connection": "epss2", "time": 1753685651.576027, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 32, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_1_route_ep.item.item-code_action_read')", "duration": 13.85, "connection": "epss2", "time": 1753685651.587438, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 161, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 32, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_1_route_ep.item.item-code_action_read')", "duration": 8.85, "connection": "epss2", "time": 1753685651.6031358, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 32, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_1_route_ep.item.unspsc-item_action_read')", "duration": 8.78, "connection": "epss2", "time": 1753685651.613221, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 161, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 32, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_1_route_ep.item.unspsc-item_action_read')", "duration": 8.37, "connection": "epss2", "time": 1753685651.623255, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 32, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_1_route_ep.item.supplier-item_action_read')", "duration": 9.34, "connection": "epss2", "time": 1753685651.633694, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 161, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 32, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_1_route_ep.item.supplier-item_action_read')", "duration": 8.58, "connection": "epss2", "time": 1753685651.644775, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 32, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_1_route_ep.item.item-task-history_action_read')", "duration": 8.72, "connection": "epss2", "time": 1753685651.6549852, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 161, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 32, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_1_route_ep.item.item-task-history_action_read')", "duration": 9.67, "connection": "epss2", "time": 1753685651.664692, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 32, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_1_route_report_action_read')", "duration": 9.08, "connection": "epss2", "time": 1753685651.675669, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 161, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 32, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}, {"call": "Livewire\\Wrapped->__call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Features\\SupportLifecycleHooks\\SupportLifecycleHooks.php", "line": 134, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_1_route_report_action_read')", "duration": 13.19, "connection": "epss2", "time": 1753685651.686756, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 32, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}, {"call": "Livewire\\Wrapped->__call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Features\\SupportLifecycleHooks\\SupportLifecycleHooks.php", "line": 134, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_1_route_report.revenue-report.today-trans_action_read')", "duration": 9.13, "connection": "epss2", "time": 1753685651.702292, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 161, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 32, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_1_route_report.revenue-report.today-trans_action_read')", "duration": 9.15, "connection": "epss2", "time": 1753685651.713273, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 32, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_1_route_report.revenue-report.accumulative_action_read')", "duration": 9.57, "connection": "epss2", "time": 1753685651.7250001, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 161, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 32, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_1_route_report.revenue-report.accumulative_action_read')", "duration": 9.98, "connection": "epss2", "time": 1753685651.736284, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 32, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_1_route_report.revenue-report.daily-summary_action_read')", "duration": 9.81, "connection": "epss2", "time": 1753685651.747713, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 161, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 32, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_1_route_report.revenue-report.daily-summary_action_read')", "duration": 10.14, "connection": "epss2", "time": 1753685651.759105, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 32, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_1_route_report.revenue-report.pending-transaction_action_read')", "duration": 9.11, "connection": "epss2", "time": 1753685651.771079, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 161, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 32, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_1_route_report.revenue-report.pending-transaction_action_read')", "duration": 9.95, "connection": "epss2", "time": 1753685651.781088, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 32, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_1_route_bpm_action_read')", "duration": 15.28, "connection": "epss2", "time": 1753685651.792471, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 161, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 32, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}, {"call": "Livewire\\Wrapped->__call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Features\\SupportLifecycleHooks\\SupportLifecycleHooks.php", "line": 134, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_1_route_bpm_action_read')", "duration": 8.83, "connection": "epss2", "time": 1753685651.8090138, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 32, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}, {"call": "Livewire\\Wrapped->__call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Features\\SupportLifecycleHooks\\SupportLifecycleHooks.php", "line": 134, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_1_route_bpm.bpm-api.find-task_action_read')", "duration": 10.29, "connection": "epss2", "time": 1753685651.820074, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 161, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 32, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_1_route_bpm.bpm-api.find-task_action_read')", "duration": 7.74, "connection": "epss2", "time": 1753685651.831235, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 32, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_1_route_bpm.bpm-api.instance-query_action_read')", "duration": 8.62, "connection": "epss2", "time": 1753685651.839849, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 161, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 32, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_1_route_bpm.bpm-api.instance-query_action_read')", "duration": 8.43, "connection": "epss2", "time": 1753685651.849916, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 32, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_1_route_bpm.bpm-api.process-manager_action_read')", "duration": 9.44, "connection": "epss2", "time": 1753685651.860443, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 161, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 32, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_1_route_bpm.bpm-api.process-manager_action_read')", "duration": 9.55, "connection": "epss2", "time": 1753685651.871595, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 32, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_1_route_bpm.bpm-api.worklist-manager_action_read')", "duration": 8.96, "connection": "epss2", "time": 1753685651.882358, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 161, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 32, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_1_route_bpm.bpm-api.worklist-manager_action_read')", "duration": 13.24, "connection": "epss2", "time": 1753685651.892781, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 32, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_1_route_bpm.bpm-api.service-manager_action_read')", "duration": 8.42, "connection": "epss2", "time": 1753685651.909315, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 161, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 32, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_1_route_bpm.bpm-api.service-manager_action_read')", "duration": 8.69, "connection": "epss2", "time": 1753685651.919952, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 32, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_1_route_bpm.bpm-api.error-handler_action_read')", "duration": 8.41, "connection": "epss2", "time": 1753685651.930202, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 161, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 32, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_1_route_bpm.bpm-api.error-handler_action_read')", "duration": 8.15, "connection": "epss2", "time": 1753685651.940172, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 32, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_1_route_bpm.bpm-api.component-instance_action_read')", "duration": 8.35, "connection": "epss2", "time": 1753685651.950236, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 161, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 32, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_1_route_bpm.bpm-api.component-instance_action_read')", "duration": 10.03, "connection": "epss2", "time": 1753685651.960057, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 32, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_1_route_settings_action_read')", "duration": 9.02, "connection": "epss2", "time": 1753685651.9711058, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 161, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 32, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}, {"call": "Livewire\\Wrapped->__call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Features\\SupportLifecycleHooks\\SupportLifecycleHooks.php", "line": 134, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_1_route_settings_action_read')", "duration": 8.57, "connection": "epss2", "time": 1753685651.981861, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 32, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}, {"call": "Livewire\\Wrapped->__call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Features\\SupportLifecycleHooks\\SupportLifecycleHooks.php", "line": 134, "isVendor": true}], "model": null, "tags": []}, {"query": "UPDATE `sessions` SET `payload` = 'YTo0OntzOjY6Il90b2tlbiI7czo0MDoicDNtcnZlMHB4YjNmQ05FWUtKZVZ3Um84TFY3VENZSW90UG8zRHhyVCI7czo2OiJfZmxhc2giO2E6Mjp7czozOiJvbGQiO2E6MDp7fXM6MzoibmV3IjthOjA6e319czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6MjU6Imh0dHA6Ly9sb2NhbGhvc3Q6ODAwMC9jcm0iO31zOjUwOiJsb2dpbl93ZWJfNTliYTM2YWRkYzJiMmY5NDAxNTgwZjAxNGM3ZjU4ZWE0ZTMwOTg5ZCI7aToxO30=', `last_activity` = 1753685652, `user_id` = 1, `ip_address` = '127.0.0.1', `user_agent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' WHERE `id` = 'OedfnZ6nkElQ8MPwP2x8fU0cPiU7786JmxmSwmTv'", "duration": 11.13, "connection": "epss2", "time": 1753685652.0386279, "trace": [{"call": "Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():167}()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware.php", "line": 19, "isVendor": true}, {"call": "Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php", "line": 21, "isVendor": true}, {"call": "Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php", "line": 31, "isVendor": true}, {"call": "Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php", "line": 21, "isVendor": true}, {"call": "Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php", "line": 51, "isVendor": true}, {"call": "Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php", "line": 27, "isVendor": true}, {"call": "Illuminate\\Http\\Middleware\\ValidatePostSize->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208, "isVendor": true}], "model": null, "tags": []}], "databaseQueriesCount": 69, "databaseSlowQueries": 0, "databaseSelects": 68, "databaseInserts": 0, "databaseUpdates": 1, "databaseDeletes": 0, "databaseOthers": 0, "databaseDuration": 697.31, "cacheQueries": [{"type": "hit", "key": "user_1_route_crm_action_read", "expiration": null, "time": 1753685651.235163, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 161, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Http\\Middleware\\CheckRoutePermission.php", "line": 39, "isVendor": false}, {"call": "App\\Http\\Middleware\\CheckRoutePermission->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()", "file": "E:\\workspace\\epss-revamp\\app\\Http\\Middleware\\LogUserActivity.php", "line": 26, "isVendor": false}, {"call": "App\\Http\\Middleware\\LogUserActivity->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50, "isVendor": true}, {"call": "Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 63, "isVendor": true}, {"call": "Illuminate\\Auth\\Middleware\\Authenticate->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php", "line": 87, "isVendor": true}]}, {"type": "hit", "key": "user_1_route_crm_action_read", "expiration": null, "time": 1753685651.245376, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Http\\Middleware\\CheckRoutePermission.php", "line": 39, "isVendor": false}, {"call": "App\\Http\\Middleware\\CheckRoutePermission->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()", "file": "E:\\workspace\\epss-revamp\\app\\Http\\Middleware\\LogUserActivity.php", "line": 26, "isVendor": false}, {"call": "App\\Http\\Middleware\\LogUserActivity->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50, "isVendor": true}, {"call": "Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 63, "isVendor": true}, {"call": "Illuminate\\Auth\\Middleware\\Authenticate->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php", "line": 87, "isVendor": true}]}, {"type": "hit", "key": "user_1_route_crm_action_read", "expiration": null, "time": 1753685651.282268, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 161, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 32, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}, {"call": "Livewire\\Wrapped->__call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Features\\SupportLifecycleHooks\\SupportLifecycleHooks.php", "line": 134, "isVendor": true}]}, {"type": "hit", "key": "user_1_route_crm_action_read", "expiration": null, "time": 1753685651.295829, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 32, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}, {"call": "Livewire\\Wrapped->__call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Features\\SupportLifecycleHooks\\SupportLifecycleHooks.php", "line": 134, "isVendor": true}]}, {"type": "hit", "key": "user_1_route_crm.cs-cases_action_read", "expiration": null, "time": 1753685651.307585, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 161, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 32, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}]}, {"type": "hit", "key": "user_1_route_crm.cs-cases_action_read", "expiration": null, "time": 1753685651.31786, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 32, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}]}, {"type": "hit", "key": "user_1_route_crm.cs-pending-input_action_read", "expiration": null, "time": 1753685651.327507, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 161, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 32, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}]}, {"type": "hit", "key": "user_1_route_crm.cs-pending-input_action_read", "expiration": null, "time": 1753685651.337001, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 32, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}]}, {"type": "hit", "key": "user_1_route_crm.poms-talkdesk_action_read", "expiration": null, "time": 1753685651.346939, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 161, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 32, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}]}, {"type": "hit", "key": "user_1_route_crm.poms-talkdesk_action_read", "expiration": null, "time": 1753685651.355764, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 32, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}]}, {"type": "hit", "key": "user_1_route_ep_action_read", "expiration": null, "time": 1753685651.364691, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 161, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 32, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}, {"call": "Livewire\\Wrapped->__call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Features\\SupportLifecycleHooks\\SupportLifecycleHooks.php", "line": 134, "isVendor": true}]}, {"type": "hit", "key": "user_1_route_ep_action_read", "expiration": null, "time": 1753685651.374993, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 32, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}, {"call": "Livewire\\Wrapped->__call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Features\\SupportLifecycleHooks\\SupportLifecycleHooks.php", "line": 134, "isVendor": true}]}, {"type": "hit", "key": "user_1_route_ep.qt.dashboard_action_read", "expiration": null, "time": 1753685651.386456, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 161, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 32, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}]}, {"type": "hit", "key": "user_1_route_ep.qt.dashboard_action_read", "expiration": null, "time": 1753685651.40128, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 32, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}]}, {"type": "hit", "key": "user_1_route_ep.qt.summary_action_read", "expiration": null, "time": 1753685651.412514, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 161, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 32, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}]}, {"type": "hit", "key": "user_1_route_ep.qt.summary_action_read", "expiration": null, "time": 1753685651.423458, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 32, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}]}, {"type": "hit", "key": "user_1_route_ep.profile.my-identity_action_read", "expiration": null, "time": 1753685651.434731, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 161, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 32, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}]}, {"type": "hit", "key": "user_1_route_ep.profile.my-identity_action_read", "expiration": null, "time": 1753685651.445859, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 32, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}]}, {"type": "hit", "key": "user_1_route_ep.profile.user-login_action_read", "expiration": null, "time": 1753685651.45781, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 161, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 32, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}]}, {"type": "hit", "key": "user_1_route_ep.profile.user-login_action_read", "expiration": null, "time": 1753685651.469273, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 32, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}]}, {"type": "hit", "key": "user_1_route_ep.profile.organization_action_read", "expiration": null, "time": 1753685651.480293, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 161, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 32, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}]}, {"type": "hit", "key": "user_1_route_ep.profile.organization_action_read", "expiration": null, "time": 1753685651.495837, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 32, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}]}, {"type": "hit", "key": "user_1_route_ep.supplier-technical_action_read", "expiration": null, "time": 1753685651.507048, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 161, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 32, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}]}, {"type": "hit", "key": "user_1_route_ep.supplier-technical_action_read", "expiration": null, "time": 1753685651.519609, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 32, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}]}, {"type": "hit", "key": "user_1_route_ep.supplier-cs_action_read", "expiration": null, "time": 1753685651.532075, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 161, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 32, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}]}, {"type": "hit", "key": "user_1_route_ep.supplier-cs_action_read", "expiration": null, "time": 1753685651.543702, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 32, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}]}, {"type": "hit", "key": "user_1_route_ep.tracking-diary_action_read", "expiration": null, "time": 1753685651.554157, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 161, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 32, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}]}, {"type": "hit", "key": "user_1_route_ep.tracking-diary_action_read", "expiration": null, "time": 1753685651.563681, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 32, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}]}, {"type": "hit", "key": "user_1_route_ep.item.uom_action_read", "expiration": null, "time": 1753685651.574821, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 161, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 32, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}]}, {"type": "hit", "key": "user_1_route_ep.item.uom_action_read", "expiration": null, "time": 1753685651.585805, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 32, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}]}, {"type": "hit", "key": "user_1_route_ep.item.item-code_action_read", "expiration": null, "time": 1753685651.602255, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 161, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 32, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}]}, {"type": "hit", "key": "user_1_route_ep.item.item-code_action_read", "expiration": null, "time": 1753685651.612448, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 32, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}]}, {"type": "hit", "key": "user_1_route_ep.item.unspsc-item_action_read", "expiration": null, "time": 1753685651.622357, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 161, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 32, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}]}, {"type": "hit", "key": "user_1_route_ep.item.unspsc-item_action_read", "expiration": null, "time": 1753685651.632358, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 32, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}]}, {"type": "hit", "key": "user_1_route_ep.item.supplier-item_action_read", "expiration": null, "time": 1753685651.643765, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 161, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 32, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}]}, {"type": "hit", "key": "user_1_route_ep.item.supplier-item_action_read", "expiration": null, "time": 1753685651.653672, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 32, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}]}, {"type": "hit", "key": "user_1_route_ep.item.item-task-history_action_read", "expiration": null, "time": 1753685651.664157, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 161, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 32, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}]}, {"type": "hit", "key": "user_1_route_ep.item.item-task-history_action_read", "expiration": null, "time": 1753685651.674661, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 32, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}]}, {"type": "hit", "key": "user_1_route_report_action_read", "expiration": null, "time": 1753685651.685519, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 161, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 32, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}, {"call": "Livewire\\Wrapped->__call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Features\\SupportLifecycleHooks\\SupportLifecycleHooks.php", "line": 134, "isVendor": true}]}, {"type": "hit", "key": "user_1_route_report_action_read", "expiration": null, "time": 1753685651.700931, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 32, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}, {"call": "Livewire\\Wrapped->__call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Features\\SupportLifecycleHooks\\SupportLifecycleHooks.php", "line": 134, "isVendor": true}]}, {"type": "hit", "key": "user_1_route_report.revenue-report.today-trans_action_read", "expiration": null, "time": 1753685651.71193, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 161, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 32, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}]}, {"type": "hit", "key": "user_1_route_report.revenue-report.today-trans_action_read", "expiration": null, "time": 1753685651.723414, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 32, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}]}, {"type": "hit", "key": "user_1_route_report.revenue-report.accumulative_action_read", "expiration": null, "time": 1753685651.735439, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 161, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 32, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}]}, {"type": "hit", "key": "user_1_route_report.revenue-report.accumulative_action_read", "expiration": null, "time": 1753685651.74668, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 32, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}]}, {"type": "hit", "key": "user_1_route_report.revenue-report.daily-summary_action_read", "expiration": null, "time": 1753685651.757976, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 161, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 32, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}]}, {"type": "hit", "key": "user_1_route_report.revenue-report.daily-summary_action_read", "expiration": null, "time": 1753685651.770106, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 32, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}]}, {"type": "hit", "key": "user_1_route_report.revenue-report.pending-transaction_action_read", "expiration": null, "time": 1753685651.780569, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 161, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 32, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}]}, {"type": "hit", "key": "user_1_route_report.revenue-report.pending-transaction_action_read", "expiration": null, "time": 1753685651.791536, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 32, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}]}, {"type": "hit", "key": "user_1_route_bpm_action_read", "expiration": null, "time": 1753685651.808013, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 161, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 32, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}, {"call": "Livewire\\Wrapped->__call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Features\\SupportLifecycleHooks\\SupportLifecycleHooks.php", "line": 134, "isVendor": true}]}, {"type": "hit", "key": "user_1_route_bpm_action_read", "expiration": null, "time": 1753685651.819123, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 32, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}, {"call": "Livewire\\Wrapped->__call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Features\\SupportLifecycleHooks\\SupportLifecycleHooks.php", "line": 134, "isVendor": true}]}, {"type": "hit", "key": "user_1_route_bpm.bpm-api.find-task_action_read", "expiration": null, "time": 1753685651.830754, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 161, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 32, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}]}, {"type": "hit", "key": "user_1_route_bpm.bpm-api.find-task_action_read", "expiration": null, "time": 1753685651.839209, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 32, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}]}, {"type": "hit", "key": "user_1_route_bpm.bpm-api.instance-query_action_read", "expiration": null, "time": 1753685651.849004, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 161, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 32, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}]}, {"type": "hit", "key": "user_1_route_bpm.bpm-api.instance-query_action_read", "expiration": null, "time": 1753685651.85891, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 32, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}]}, {"type": "hit", "key": "user_1_route_bpm.bpm-api.process-manager_action_read", "expiration": null, "time": 1753685651.870636, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 161, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 32, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}]}, {"type": "hit", "key": "user_1_route_bpm.bpm-api.process-manager_action_read", "expiration": null, "time": 1753685651.881771, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 32, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}]}, {"type": "hit", "key": "user_1_route_bpm.bpm-api.worklist-manager_action_read", "expiration": null, "time": 1753685651.891608, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 161, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 32, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}]}, {"type": "hit", "key": "user_1_route_bpm.bpm-api.worklist-manager_action_read", "expiration": null, "time": 1753685651.907203, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 32, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}]}, {"type": "hit", "key": "user_1_route_bpm.bpm-api.service-manager_action_read", "expiration": null, "time": 1753685651.918871, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 161, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 32, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}]}, {"type": "hit", "key": "user_1_route_bpm.bpm-api.service-manager_action_read", "expiration": null, "time": 1753685651.92915, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 32, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}]}, {"type": "hit", "key": "user_1_route_bpm.bpm-api.error-handler_action_read", "expiration": null, "time": 1753685651.93922, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 161, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 32, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}]}, {"type": "hit", "key": "user_1_route_bpm.bpm-api.error-handler_action_read", "expiration": null, "time": 1753685651.949078, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 32, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}]}, {"type": "hit", "key": "user_1_route_bpm.bpm-api.component-instance_action_read", "expiration": null, "time": 1753685651.959386, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 161, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 32, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}]}, {"type": "hit", "key": "user_1_route_bpm.bpm-api.component-instance_action_read", "expiration": null, "time": 1753685651.970408, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 32, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}]}, {"type": "hit", "key": "user_1_route_settings_action_read", "expiration": null, "time": 1753685651.980554, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 161, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 32, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}, {"call": "Livewire\\Wrapped->__call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Features\\SupportLifecycleHooks\\SupportLifecycleHooks.php", "line": 134, "isVendor": true}]}, {"type": "hit", "key": "user_1_route_settings_action_read", "expiration": null, "time": 1753685651.99091, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 32, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}, {"call": "Livewire\\Wrapped->__call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Features\\SupportLifecycleHooks\\SupportLifecycleHooks.php", "line": 134, "isVendor": true}]}], "cacheReads": 66, "cacheHits": 66, "cacheWrites": 0, "cacheDeletes": 0, "cacheTime": 0, "modelsActions": [], "modelsRetrieved": {"App\\Models\\User": 1}, "modelsCreated": [], "modelsUpdated": [], "modelsDeleted": [], "redisCommands": [], "queueJobs": [], "timelineData": [{"description": "Controller", "start": 1753685651.09346, "end": 1753685652.050468, "duration": 957.007884979248, "color": null, "data": null}], "log": [{"message": "User Activity", "exception": null, "context": {"__type__": "array", "user_id": 1, "name": "Super Admin", "email": "<EMAIL>", "url": "http://localhost:8000/crm", "method": "GET", "ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}, "level": "info", "time": 1753685651.219059, "trace": [{"call": "Illuminate\\Support\\Facades\\Facade::__callStatic()", "file": "E:\\workspace\\epss-revamp\\app\\Http\\Middleware\\LogUserActivity.php", "line": 16, "isVendor": false}, {"call": "App\\Http\\Middleware\\LogUserActivity->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50, "isVendor": true}, {"call": "Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 63, "isVendor": true}, {"call": "Illuminate\\Auth\\Middleware\\Authenticate->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php", "line": 87, "isVendor": true}, {"call": "Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php", "line": 48, "isVendor": true}, {"call": "Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208, "isVendor": true}]}], "events": [], "routes": [{"method": "GET, HEAD", "uri": "login", "name": "login", "action": "App\\Livewire\\Login", "middleware": ["web"], "before": "", "after": ""}, {"method": "GET, HEAD", "uri": "home", "name": "home", "action": "App\\Livewire\\HomePage", "middleware": ["web", "auth:web", "log.activity"], "before": "", "after": ""}, {"method": "GET, HEAD", "uri": "profile", "name": "profile", "action": "App\\Livewire\\UserProfile", "middleware": ["web", "auth:web", "log.activity"], "before": "", "after": ""}, {"method": "GET, HEAD", "uri": "crm", "name": "crm", "action": "App\\Livewire\\Project\\CRM", "middleware": ["web", "auth:web", "log.activity", "route.permission"], "before": "", "after": ""}, {"method": "GET, HEAD", "uri": "ep", "name": "ep", "action": "App\\Livewire\\Project\\EPerolehan", "middleware": ["web", "auth:web", "log.activity", "route.permission"], "before": "", "after": ""}, {"method": "GET, HEAD", "uri": "report", "name": "report", "action": "App\\Livewire\\Project\\Report", "middleware": ["web", "auth:web", "log.activity", "route.permission"], "before": "", "after": ""}, {"method": "GET, HEAD", "uri": "bpm", "name": "bpm", "action": "App\\Livewire\\Project\\Bpm", "middleware": ["web", "auth:web", "log.activity", "route.permission"], "before": "", "after": ""}, {"method": "GET, HEAD", "uri": "settings", "name": "settings", "action": "App\\Livewire\\AppSetting", "middleware": ["web", "auth:web", "log.activity", "route.permission"], "before": "", "after": ""}], "notifications": [], "emailsData": [], "viewsData": [{"description": "Rendering a view", "start": 1753685651.256902, "end": 1753685651.256902, "duration": 0, "color": null, "data": {"name": "livewire.project.crm.index", "data": {"__type__": "array", "activeTab": null}}}, {"description": "Rendering a view", "start": 1753685651.263624, "end": 1753685651.263624, "duration": 0, "color": null, "data": {"name": "__components::4943bc92ebba41e8b0e508149542e0ad", "data": {"__type__": "array", "content": "<div wire:snapshot=\"{&quot;data&quot;:{&quot;activeTab&quot;:null},&quot;memo&quot;:{&quot;id&quot;:&quot;sReYd0IfiqXQRlg9p74z&quot;,&quot;name&quot;:&quot;project.c-r-m&quot;,&quot;path&quot;:&quot;crm&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;children&quot;:[],&quot;scripts&quot;:[],&quot;assets&quot;:[],&quot;errors&quot;:[],&quot;locale&quot;:&quot;en&quot;},&quot;checksum&quot;:&quot;a0886d53d7d21eb24354852ecf8c665acd523295f2d0b42f2598dbdc7b0705df&quot;}\" wire:effects=\"[]\" wire:id=\"sReYd0IfiqXQRlg9p74z\">\n    <div class=\"d-flex flex-column flex-lg-row\">\n        <!-- Tabs navigation -->\n        <div class=\"d-flex flex-column flex-lg-row gap-5 gap-lg-0\">\n            <!-- Dynamic component container -->\n            <div class=\"tab-content flex-grow-1\">\n                <!--[if BLOCK]><![endif]--><!--[if ENDBLOCK]><![endif]-->\n            </div>\n        </div>\n    </div>\n</div>", "layout": {"__class__": "Livewire\\Features\\SupportPageComponents\\PageComponentConfig", "slots": {"__type__": "array"}, "viewContext": {"__class__": "Livewire\\Mechanisms\\HandleComponents\\ViewContext", "slots": {"__type__": "array"}, "pushes": {"__type__": "array"}, "prepends": {"__type__": "array"}, "sections": {"__type__": "array"}}, "response": null, "type": "component", "view": "layouts.app", "slotOrSection": "slot", "params": {"__type__": "array", "attributes": {"__class__": "Illuminate\\View\\ComponentAttributeBag", "*attributes": {"__type__": "array"}}}}}}}, {"description": "Rendering a view", "start": 1753685651.266629, "end": 1753685651.266629, "duration": 0, "color": null, "data": {"name": "layouts.app", "data": {"__type__": "array", "attributes": {"__class__": "Illuminate\\View\\ComponentAttributeBag", "*attributes": {"__type__": "array"}}, "slot": {"__class__": "Illuminate\\View\\ComponentSlot", "attributes": {"__class__": "Illuminate\\View\\ComponentAttributeBag", "*attributes": {"__type__": "array"}}, "*contents": "<div wire:snapshot=\"{&quot;data&quot;:{&quot;activeTab&quot;:null},&quot;memo&quot;:{&quot;id&quot;:&quot;sReYd0IfiqXQRlg9p74z&quot;,&quot;name&quot;:&quot;project.c-r-m&quot;,&quot;path&quot;:&quot;crm&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;children&quot;:[],&quot;scripts&quot;:[],&quot;assets&quot;:[],&quot;errors&quot;:[],&quot;locale&quot;:&quot;en&quot;},&quot;checksum&quot;:&quot;a0886d53d7d21eb24354852ecf8c665acd523295f2d0b42f2598dbdc7b0705df&quot;}\" wire:effects=\"[]\" wire:id=\"sReYd0IfiqXQRlg9p74z\">\n    <div class=\"d-flex flex-column flex-lg-row\">\n        <!-- Tabs navigation -->\n        <div class=\"d-flex flex-column flex-lg-row gap-5 gap-lg-0\">\n            <!-- Dynamic component container -->\n            <div class=\"tab-content flex-grow-1\">\n                <!--[if BLOCK]><![endif]--><!--[if ENDBLOCK]><![endif]-->\n            </div>\n        </div>\n    </div>\n</div>"}}}}, {"description": "Rendering a view", "start": 1753685651.993566, "end": 1753685651.993566, "duration": 0, "color": null, "data": {"name": "livewire.component.sidebar", "data": {"__type__": "array", "menuItems": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Home", "id": "home", "icon": "ki-home", "url": "http://localhost:8000/home", "active": false}, "1": {"__type__": "array", "type": "item", "title": "CRM", "id": "crm", "icon": "ki-information", "url": "http://localhost:8000/crm", "active": true, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "CRM - CS Cases", "id": "cs-cases", "url": "http://localhost:8000/crm?tab=cs-cases", "active": false}, "1": {"__type__": "array", "type": "item", "title": "CRM - CS Pending Input", "id": "cs-pending-input", "url": "http://localhost:8000/crm?tab=cs-pending-input", "active": false}, "2": {"__type__": "array", "type": "item", "title": "POMS - Talkdesk", "id": "poms-talkdesk", "url": "http://localhost:8000/crm?tab=poms-talkdesk", "active": false}}}, "2": {"__type__": "array", "type": "section", "title": "ePerolehan", "id": "eper<PERSON>han", "icon": "ki-document", "url": "http://localhost:8000/ep", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "section", "title": "Quotation Tender", "id": "qt", "url": "http://localhost:8000/ep?tab=qt", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Dashboard", "id": "qt-dashboard", "url": "http://localhost:8000/ep?tab=qt-dashboard", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Summary", "id": "summary", "url": "http://localhost:8000/ep?tab=qt-summary", "active": false}}}, "1": {"__type__": "array", "type": "section", "title": "Profile", "id": "profile", "url": "http://localhost:8000/ep?tab=profile", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "MyIdentity", "id": "my-identity", "url": "http://localhost:8000/ep?tab=my-identity", "active": false}, "1": {"__type__": "array", "type": "item", "title": "User Login", "id": "user-login", "url": "http://localhost:8000/ep?tab=user-login", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Find Organization", "id": "find-organization", "url": "http://localhost:8000/ep?tab=organization", "active": false}}}, "2": {"__type__": "array", "type": "section", "title": "Supplier", "id": "supplier", "url": "http://localhost:8000/ep?tab=supplier", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Find Supplier", "id": "find-supplier-technical", "url": "http://localhost:8000/ep?tab=supplier-technical", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Find Supplier", "id": "find-supplier-cs", "url": "http://localhost:8000/ep?tab=supplier-cs", "active": false}}}, "3": {"__type__": "array", "type": "item", "title": "Tracking Diary", "id": "tracking-diary", "url": "http://localhost:8000/ep?tab=tracking-diary", "active": false}, "4": {"__type__": "array", "type": "section", "title": "<PERSON><PERSON>", "id": "item", "url": "http://localhost:8000/ep?tab=item", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Find UOM", "id": "find-uom", "url": "http://localhost:8000/ep?tab=uom", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Find Items", "id": "find-items", "url": "http://localhost:8000/ep?tab=item-code", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Find UNSPSC Items", "id": "find-unspsc-items", "url": "http://localhost:8000/ep?tab=unspsc-item", "active": false}, "3": {"__type__": "array", "type": "item", "title": "Find Supplier's Item", "id": "find-supplier-item", "url": "http://localhost:8000/ep?tab=supplier-item", "active": false}, "4": {"__type__": "array", "type": "item", "title": "Find Item Task History", "id": "item-task-history", "url": "http://localhost:8000/ep?tab=item-history", "active": false}}}}}, "3": {"__type__": "array", "type": "item", "title": "REPORT - REVENUE", "id": "report", "icon": "ki-graph-up", "url": "http://localhost:8000/report", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Today Transaction", "id": "today-trans", "url": "http://localhost:8000/report?tab=today-trans", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Accumulative YTD & MTD", "id": "accumulative", "url": "http://localhost:8000/report?tab=accumulative", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Daily Summary", "id": "daily-summary", "url": "http://localhost:8000/report?tab=daily-summary", "active": false}, "3": {"__type__": "array", "type": "item", "title": "Pending Transaction", "id": "pending-transaction", "url": "http://localhost:8000/report?tab=pending-transaction", "active": false}}}, "4": {"__type__": "array", "type": "item", "title": "BPM", "id": "bpm", "icon": "ki-wrench", "url": "http://localhost:8000/bpm", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Find Task", "id": "find-task", "url": "http://localhost:8000/bpm?tab=find-task", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Instance Query", "id": "instance-query", "url": "http://localhost:8000/bpm?tab=instance-query", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Process Manager", "id": "process-manager", "url": "http://localhost:8000/bpm?tab=process-manager", "active": false}, "3": {"__type__": "array", "type": "item", "title": "Worklist Manager", "id": "worklist-manager", "url": "http://localhost:8000/bpm?tab=worklist-manager", "active": false}, "4": {"__type__": "array", "type": "item", "title": "Service Manager", "id": "service-manager", "url": "http://localhost:8000/bpm?tab=service-manager", "active": false}, "5": {"__type__": "array", "type": "item", "title": "<PERSON><PERSON><PERSON>", "id": "error-handler", "url": "http://localhost:8000/bpm?tab=error-handler", "active": false}, "6": {"__type__": "array", "type": "item", "title": "Track Comp. Instance", "id": "component-instance", "url": "http://localhost:8000/bpm?tab=component-instance", "active": false}}}, "5": {"__type__": "array", "type": "section", "title": "App Settings", "id": "settings", "icon": "ki-setting", "hidden": true, "url": "http://localhost:8000/settings?tab=users", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Users", "id": "users", "url": "http://localhost:8000/settings?tab=users", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Groups", "id": "groups", "url": "http://localhost:8000/settings?tab=groups", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Roles", "id": "roles", "url": "http://localhost:8000/settings?tab=roles", "active": false}, "3": {"__type__": "array", "type": "item", "title": "Permissions", "id": "permissions", "url": "http://localhost:8000/settings?tab=permissions", "active": false}, "4": {"__type__": "array", "type": "item", "title": "Access Control List", "id": "acl", "url": "http://localhost:8000/settings?tab=access-control-list", "active": false}}}}, "secondaryMenuItems": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "CRM", "id": "crm", "icon": "ki-information", "url": "http://localhost:8000/crm", "active": true, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "CRM - CS Cases", "id": "cs-cases", "url": "http://localhost:8000/crm?tab=cs-cases", "active": false}, "1": {"__type__": "array", "type": "item", "title": "CRM - CS Pending Input", "id": "cs-pending-input", "url": "http://localhost:8000/crm?tab=cs-pending-input", "active": false}, "2": {"__type__": "array", "type": "item", "title": "POMS - Talkdesk", "id": "poms-talkdesk", "url": "http://localhost:8000/crm?tab=poms-talkdesk", "active": false}}}}, "user": {"__type__": "array", "first_name": "Super", "last_name": "Admin", "name": "Super Admin", "user_name": "eps<PERSON><PERSON>", "email": "<EMAIL>"}, "currentRoute": "http://localhost:8000/crm", "activeTopLevelItem": "crm"}}}, {"description": "Rendering a view", "start": 1753685652.000788, "end": 1753685652.000788, "duration": 0, "color": null, "data": {"name": "partials.sidebar.header", "data": {"__type__": "array", "app": {"__class__": "Illuminate\\Foundation\\Application", "__omitted__": "blackbox"}, "errors": {"__class__": "Illuminate\\Support\\ViewErrorBag", "*bags": {"__type__": "array", "default": {"__class__": "Illuminate\\Support\\MessageBag", "*messages": {"__type__": "array"}, "*format": ":message"}}}, "_instance": {"__class__": "App\\Livewire\\Component\\Sidebar", "*__id": "RJSgk1CyvvZqXhSHvCpD", "*__name": "component.sidebar", "*listeners": {"__type__": "array"}, "*attributes": {"__class__": "Livewire\\Features\\SupportAttributes\\AttributeCollection", "*items": {"__type__": "array"}, "*escapeWhenCastingToString": false}, "*withValidatorCallback": null, "*rulesFromOutside": {"__type__": "array"}, "*messagesFromOutside": {"__type__": "array"}, "*validationAttributesFromOutside": {"__type__": "array"}, "user": {"__type__": "array", "first_name": "Super", "last_name": "Admin", "name": "Super Admin", "user_name": "eps<PERSON><PERSON>", "email": "<EMAIL>"}, "menuItems": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Home", "id": "home", "icon": "ki-home", "url": "http://localhost:8000/home", "active": false}, "1": {"__type__": "array", "type": "item", "title": "CRM", "id": "crm", "icon": "ki-information", "url": "http://localhost:8000/crm", "active": true, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "CRM - CS Cases", "id": "cs-cases", "url": "http://localhost:8000/crm?tab=cs-cases", "active": false}, "1": {"__type__": "array", "type": "item", "title": "CRM - CS Pending Input", "id": "cs-pending-input", "url": "http://localhost:8000/crm?tab=cs-pending-input", "active": false}, "2": {"__type__": "array", "type": "item", "title": "POMS - Talkdesk", "id": "poms-talkdesk", "url": "http://localhost:8000/crm?tab=poms-talkdesk", "active": false}}}, "2": {"__type__": "array", "type": "section", "title": "ePerolehan", "id": "eper<PERSON>han", "icon": "ki-document", "url": "http://localhost:8000/ep", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "section", "title": "Quotation Tender", "id": "qt", "url": "http://localhost:8000/ep?tab=qt", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Dashboard", "id": "qt-dashboard", "url": "http://localhost:8000/ep?tab=qt-dashboard", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Summary", "id": "summary", "url": "http://localhost:8000/ep?tab=qt-summary", "active": false}}}, "1": {"__type__": "array", "type": "section", "title": "Profile", "id": "profile", "url": "http://localhost:8000/ep?tab=profile", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "MyIdentity", "id": "my-identity", "url": "http://localhost:8000/ep?tab=my-identity", "active": false}, "1": {"__type__": "array", "type": "item", "title": "User Login", "id": "user-login", "url": "http://localhost:8000/ep?tab=user-login", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Find Organization", "id": "find-organization", "url": "http://localhost:8000/ep?tab=organization", "active": false}}}, "2": {"__type__": "array", "type": "section", "title": "Supplier", "id": "supplier", "url": "http://localhost:8000/ep?tab=supplier", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Find Supplier", "id": "find-supplier-technical", "url": "http://localhost:8000/ep?tab=supplier-technical", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Find Supplier", "id": "find-supplier-cs", "url": "http://localhost:8000/ep?tab=supplier-cs", "active": false}}}, "3": {"__type__": "array", "type": "item", "title": "Tracking Diary", "id": "tracking-diary", "url": "http://localhost:8000/ep?tab=tracking-diary", "active": false}, "4": {"__type__": "array", "type": "section", "title": "<PERSON><PERSON>", "id": "item", "url": "http://localhost:8000/ep?tab=item", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Find UOM", "id": "find-uom", "url": "http://localhost:8000/ep?tab=uom", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Find Items", "id": "find-items", "url": "http://localhost:8000/ep?tab=item-code", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Find UNSPSC Items", "id": "find-unspsc-items", "url": "http://localhost:8000/ep?tab=unspsc-item", "active": false}, "3": {"__type__": "array", "type": "item", "title": "Find Supplier's Item", "id": "find-supplier-item", "url": "http://localhost:8000/ep?tab=supplier-item", "active": false}, "4": {"__type__": "array", "type": "item", "title": "Find Item Task History", "id": "item-task-history", "url": "http://localhost:8000/ep?tab=item-history", "active": false}}}}}, "3": {"__type__": "array", "type": "item", "title": "REPORT - REVENUE", "id": "report", "icon": "ki-graph-up", "url": "http://localhost:8000/report", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Today Transaction", "id": "today-trans", "url": "http://localhost:8000/report?tab=today-trans", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Accumulative YTD & MTD", "id": "accumulative", "url": "http://localhost:8000/report?tab=accumulative", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Daily Summary", "id": "daily-summary", "url": "http://localhost:8000/report?tab=daily-summary", "active": false}, "3": {"__type__": "array", "type": "item", "title": "Pending Transaction", "id": "pending-transaction", "url": "http://localhost:8000/report?tab=pending-transaction", "active": false}}}, "4": {"__type__": "array", "type": "item", "title": "BPM", "id": "bpm", "icon": "ki-wrench", "url": "http://localhost:8000/bpm", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Find Task", "id": "find-task", "url": "http://localhost:8000/bpm?tab=find-task", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Instance Query", "id": "instance-query", "url": "http://localhost:8000/bpm?tab=instance-query", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Process Manager", "id": "process-manager", "url": "http://localhost:8000/bpm?tab=process-manager", "active": false}, "3": {"__type__": "array", "type": "item", "title": "Worklist Manager", "id": "worklist-manager", "url": "http://localhost:8000/bpm?tab=worklist-manager", "active": false}, "4": {"__type__": "array", "type": "item", "title": "Service Manager", "id": "service-manager", "url": "http://localhost:8000/bpm?tab=service-manager", "active": false}, "5": {"__type__": "array", "type": "item", "title": "<PERSON><PERSON><PERSON>", "id": "error-handler", "url": "http://localhost:8000/bpm?tab=error-handler", "active": false}, "6": {"__type__": "array", "type": "item", "title": "Track Comp. Instance", "id": "component-instance", "url": "http://localhost:8000/bpm?tab=component-instance", "active": false}}}, "5": {"__type__": "array", "type": "section", "title": "App Settings", "id": "settings", "icon": "ki-setting", "hidden": true, "url": "http://localhost:8000/settings?tab=users", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Users", "id": "users", "url": "http://localhost:8000/settings?tab=users", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Groups", "id": "groups", "url": "http://localhost:8000/settings?tab=groups", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Roles", "id": "roles", "url": "http://localhost:8000/settings?tab=roles", "active": false}, "3": {"__type__": "array", "type": "item", "title": "Permissions", "id": "permissions", "url": "http://localhost:8000/settings?tab=permissions", "active": false}, "4": {"__type__": "array", "type": "item", "title": "Access Control List", "id": "acl", "url": "http://localhost:8000/settings?tab=access-control-list", "active": false}}}}, "currentRoute": "http://localhost:8000/crm", "activeTopLevelItem": "crm"}, "menuItems": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Home", "id": "home", "icon": "ki-home", "url": "http://localhost:8000/home", "active": false}, "1": {"__type__": "array", "type": "item", "title": "CRM", "id": "crm", "icon": "ki-information", "url": "http://localhost:8000/crm", "active": true, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "CRM - CS Cases", "id": "cs-cases", "url": "http://localhost:8000/crm?tab=cs-cases", "active": false}, "1": {"__type__": "array", "type": "item", "title": "CRM - CS Pending Input", "id": "cs-pending-input", "url": "http://localhost:8000/crm?tab=cs-pending-input", "active": false}, "2": {"__type__": "array", "type": "item", "title": "POMS - Talkdesk", "id": "poms-talkdesk", "url": "http://localhost:8000/crm?tab=poms-talkdesk", "active": false}}}, "2": {"__type__": "array", "type": "section", "title": "ePerolehan", "id": "eper<PERSON>han", "icon": "ki-document", "url": "http://localhost:8000/ep", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "section", "title": "Quotation Tender", "id": "qt", "url": "http://localhost:8000/ep?tab=qt", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Dashboard", "id": "qt-dashboard", "url": "http://localhost:8000/ep?tab=qt-dashboard", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Summary", "id": "summary", "url": "http://localhost:8000/ep?tab=qt-summary", "active": false}}}, "1": {"__type__": "array", "type": "section", "title": "Profile", "id": "profile", "url": "http://localhost:8000/ep?tab=profile", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "MyIdentity", "id": "my-identity", "url": "http://localhost:8000/ep?tab=my-identity", "active": false}, "1": {"__type__": "array", "type": "item", "title": "User Login", "id": "user-login", "url": "http://localhost:8000/ep?tab=user-login", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Find Organization", "id": "find-organization", "url": "http://localhost:8000/ep?tab=organization", "active": false}}}, "2": {"__type__": "array", "type": "section", "title": "Supplier", "id": "supplier", "url": "http://localhost:8000/ep?tab=supplier", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Find Supplier", "id": "find-supplier-technical", "url": "http://localhost:8000/ep?tab=supplier-technical", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Find Supplier", "id": "find-supplier-cs", "url": "http://localhost:8000/ep?tab=supplier-cs", "active": false}}}, "3": {"__type__": "array", "type": "item", "title": "Tracking Diary", "id": "tracking-diary", "url": "http://localhost:8000/ep?tab=tracking-diary", "active": false}, "4": {"__type__": "array", "type": "section", "title": "<PERSON><PERSON>", "id": "item", "url": "http://localhost:8000/ep?tab=item", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Find UOM", "id": "find-uom", "url": "http://localhost:8000/ep?tab=uom", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Find Items", "id": "find-items", "url": "http://localhost:8000/ep?tab=item-code", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Find UNSPSC Items", "id": "find-unspsc-items", "url": "http://localhost:8000/ep?tab=unspsc-item", "active": false}, "3": {"__type__": "array", "type": "item", "title": "Find Supplier's Item", "id": "find-supplier-item", "url": "http://localhost:8000/ep?tab=supplier-item", "active": false}, "4": {"__type__": "array", "type": "item", "title": "Find Item Task History", "id": "item-task-history", "url": "http://localhost:8000/ep?tab=item-history", "active": false}}}}}, "3": {"__type__": "array", "type": "item", "title": "REPORT - REVENUE", "id": "report", "icon": "ki-graph-up", "url": "http://localhost:8000/report", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Today Transaction", "id": "today-trans", "url": "http://localhost:8000/report?tab=today-trans", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Accumulative YTD & MTD", "id": "accumulative", "url": "http://localhost:8000/report?tab=accumulative", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Daily Summary", "id": "daily-summary", "url": "http://localhost:8000/report?tab=daily-summary", "active": false}, "3": {"__type__": "array", "type": "item", "title": "Pending Transaction", "id": "pending-transaction", "url": "http://localhost:8000/report?tab=pending-transaction", "active": false}}}, "4": {"__type__": "array", "type": "item", "title": "BPM", "id": "bpm", "icon": "ki-wrench", "url": "http://localhost:8000/bpm", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Find Task", "id": "find-task", "url": "http://localhost:8000/bpm?tab=find-task", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Instance Query", "id": "instance-query", "url": "http://localhost:8000/bpm?tab=instance-query", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Process Manager", "id": "process-manager", "url": "http://localhost:8000/bpm?tab=process-manager", "active": false}, "3": {"__type__": "array", "type": "item", "title": "Worklist Manager", "id": "worklist-manager", "url": "http://localhost:8000/bpm?tab=worklist-manager", "active": false}, "4": {"__type__": "array", "type": "item", "title": "Service Manager", "id": "service-manager", "url": "http://localhost:8000/bpm?tab=service-manager", "active": false}, "5": {"__type__": "array", "type": "item", "title": "<PERSON><PERSON><PERSON>", "id": "error-handler", "url": "http://localhost:8000/bpm?tab=error-handler", "active": false}, "6": {"__type__": "array", "type": "item", "title": "Track Comp. Instance", "id": "component-instance", "url": "http://localhost:8000/bpm?tab=component-instance", "active": false}}}, "5": {"__type__": "array", "type": "section", "title": "App Settings", "id": "settings", "icon": "ki-setting", "hidden": true, "url": "http://localhost:8000/settings?tab=users", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Users", "id": "users", "url": "http://localhost:8000/settings?tab=users", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Groups", "id": "groups", "url": "http://localhost:8000/settings?tab=groups", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Roles", "id": "roles", "url": "http://localhost:8000/settings?tab=roles", "active": false}, "3": {"__type__": "array", "type": "item", "title": "Permissions", "id": "permissions", "url": "http://localhost:8000/settings?tab=permissions", "active": false}, "4": {"__type__": "array", "type": "item", "title": "Access Control List", "id": "acl", "url": "http://localhost:8000/settings?tab=access-control-list", "active": false}}}}, "secondaryMenuItems": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "CRM", "id": "crm", "icon": "ki-information", "url": "http://localhost:8000/crm", "active": true, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "CRM - CS Cases", "id": "cs-cases", "url": "http://localhost:8000/crm?tab=cs-cases", "active": false}, "1": {"__type__": "array", "type": "item", "title": "CRM - CS Pending Input", "id": "cs-pending-input", "url": "http://localhost:8000/crm?tab=cs-pending-input", "active": false}, "2": {"__type__": "array", "type": "item", "title": "POMS - Talkdesk", "id": "poms-talkdesk", "url": "http://localhost:8000/crm?tab=poms-talkdesk", "active": false}}}}, "user": {"__type__": "array", "first_name": "Super", "last_name": "Admin", "name": "Super Admin", "user_name": "eps<PERSON><PERSON>", "email": "<EMAIL>"}, "currentRoute": "http://localhost:8000/crm", "activeTopLevelItem": "crm"}}}, {"description": "Rendering a view", "start": 1753685652.003334, "end": 1753685652.003334, "duration": 0, "color": null, "data": {"name": "partials.sidebar.user-menu", "data": {"__type__": "array", "app": {"__class__": "Illuminate\\Foundation\\Application", "__omitted__": "blackbox"}, "errors": {"__class__": "Illuminate\\Support\\ViewErrorBag", "*bags": {"__type__": "array", "default": {"__class__": "Illuminate\\Support\\MessageBag", "*messages": {"__type__": "array"}, "*format": ":message"}}}, "_instance": {"__class__": "App\\Livewire\\Component\\Sidebar", "*__id": "RJSgk1CyvvZqXhSHvCpD", "*__name": "component.sidebar", "*listeners": {"__type__": "array"}, "*attributes": {"__class__": "Livewire\\Features\\SupportAttributes\\AttributeCollection", "*items": {"__type__": "array"}, "*escapeWhenCastingToString": false}, "*withValidatorCallback": null, "*rulesFromOutside": {"__type__": "array"}, "*messagesFromOutside": {"__type__": "array"}, "*validationAttributesFromOutside": {"__type__": "array"}, "user": {"__type__": "array", "first_name": "Super", "last_name": "Admin", "name": "Super Admin", "user_name": "eps<PERSON><PERSON>", "email": "<EMAIL>"}, "menuItems": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Home", "id": "home", "icon": "ki-home", "url": "http://localhost:8000/home", "active": false}, "1": {"__type__": "array", "type": "item", "title": "CRM", "id": "crm", "icon": "ki-information", "url": "http://localhost:8000/crm", "active": true, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "CRM - CS Cases", "id": "cs-cases", "url": "http://localhost:8000/crm?tab=cs-cases", "active": false}, "1": {"__type__": "array", "type": "item", "title": "CRM - CS Pending Input", "id": "cs-pending-input", "url": "http://localhost:8000/crm?tab=cs-pending-input", "active": false}, "2": {"__type__": "array", "type": "item", "title": "POMS - Talkdesk", "id": "poms-talkdesk", "url": "http://localhost:8000/crm?tab=poms-talkdesk", "active": false}}}, "2": {"__type__": "array", "type": "section", "title": "ePerolehan", "id": "eper<PERSON>han", "icon": "ki-document", "url": "http://localhost:8000/ep", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "section", "title": "Quotation Tender", "id": "qt", "url": "http://localhost:8000/ep?tab=qt", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Dashboard", "id": "qt-dashboard", "url": "http://localhost:8000/ep?tab=qt-dashboard", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Summary", "id": "summary", "url": "http://localhost:8000/ep?tab=qt-summary", "active": false}}}, "1": {"__type__": "array", "type": "section", "title": "Profile", "id": "profile", "url": "http://localhost:8000/ep?tab=profile", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "MyIdentity", "id": "my-identity", "url": "http://localhost:8000/ep?tab=my-identity", "active": false}, "1": {"__type__": "array", "type": "item", "title": "User Login", "id": "user-login", "url": "http://localhost:8000/ep?tab=user-login", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Find Organization", "id": "find-organization", "url": "http://localhost:8000/ep?tab=organization", "active": false}}}, "2": {"__type__": "array", "type": "section", "title": "Supplier", "id": "supplier", "url": "http://localhost:8000/ep?tab=supplier", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Find Supplier", "id": "find-supplier-technical", "url": "http://localhost:8000/ep?tab=supplier-technical", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Find Supplier", "id": "find-supplier-cs", "url": "http://localhost:8000/ep?tab=supplier-cs", "active": false}}}, "3": {"__type__": "array", "type": "item", "title": "Tracking Diary", "id": "tracking-diary", "url": "http://localhost:8000/ep?tab=tracking-diary", "active": false}, "4": {"__type__": "array", "type": "section", "title": "<PERSON><PERSON>", "id": "item", "url": "http://localhost:8000/ep?tab=item", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Find UOM", "id": "find-uom", "url": "http://localhost:8000/ep?tab=uom", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Find Items", "id": "find-items", "url": "http://localhost:8000/ep?tab=item-code", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Find UNSPSC Items", "id": "find-unspsc-items", "url": "http://localhost:8000/ep?tab=unspsc-item", "active": false}, "3": {"__type__": "array", "type": "item", "title": "Find Supplier's Item", "id": "find-supplier-item", "url": "http://localhost:8000/ep?tab=supplier-item", "active": false}, "4": {"__type__": "array", "type": "item", "title": "Find Item Task History", "id": "item-task-history", "url": "http://localhost:8000/ep?tab=item-history", "active": false}}}}}, "3": {"__type__": "array", "type": "item", "title": "REPORT - REVENUE", "id": "report", "icon": "ki-graph-up", "url": "http://localhost:8000/report", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Today Transaction", "id": "today-trans", "url": "http://localhost:8000/report?tab=today-trans", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Accumulative YTD & MTD", "id": "accumulative", "url": "http://localhost:8000/report?tab=accumulative", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Daily Summary", "id": "daily-summary", "url": "http://localhost:8000/report?tab=daily-summary", "active": false}, "3": {"__type__": "array", "type": "item", "title": "Pending Transaction", "id": "pending-transaction", "url": "http://localhost:8000/report?tab=pending-transaction", "active": false}}}, "4": {"__type__": "array", "type": "item", "title": "BPM", "id": "bpm", "icon": "ki-wrench", "url": "http://localhost:8000/bpm", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Find Task", "id": "find-task", "url": "http://localhost:8000/bpm?tab=find-task", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Instance Query", "id": "instance-query", "url": "http://localhost:8000/bpm?tab=instance-query", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Process Manager", "id": "process-manager", "url": "http://localhost:8000/bpm?tab=process-manager", "active": false}, "3": {"__type__": "array", "type": "item", "title": "Worklist Manager", "id": "worklist-manager", "url": "http://localhost:8000/bpm?tab=worklist-manager", "active": false}, "4": {"__type__": "array", "type": "item", "title": "Service Manager", "id": "service-manager", "url": "http://localhost:8000/bpm?tab=service-manager", "active": false}, "5": {"__type__": "array", "type": "item", "title": "<PERSON><PERSON><PERSON>", "id": "error-handler", "url": "http://localhost:8000/bpm?tab=error-handler", "active": false}, "6": {"__type__": "array", "type": "item", "title": "Track Comp. Instance", "id": "component-instance", "url": "http://localhost:8000/bpm?tab=component-instance", "active": false}}}, "5": {"__type__": "array", "type": "section", "title": "App Settings", "id": "settings", "icon": "ki-setting", "hidden": true, "url": "http://localhost:8000/settings?tab=users", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Users", "id": "users", "url": "http://localhost:8000/settings?tab=users", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Groups", "id": "groups", "url": "http://localhost:8000/settings?tab=groups", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Roles", "id": "roles", "url": "http://localhost:8000/settings?tab=roles", "active": false}, "3": {"__type__": "array", "type": "item", "title": "Permissions", "id": "permissions", "url": "http://localhost:8000/settings?tab=permissions", "active": false}, "4": {"__type__": "array", "type": "item", "title": "Access Control List", "id": "acl", "url": "http://localhost:8000/settings?tab=access-control-list", "active": false}}}}, "currentRoute": "http://localhost:8000/crm", "activeTopLevelItem": "crm"}, "menuItems": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Home", "id": "home", "icon": "ki-home", "url": "http://localhost:8000/home", "active": false}, "1": {"__type__": "array", "type": "item", "title": "CRM", "id": "crm", "icon": "ki-information", "url": "http://localhost:8000/crm", "active": true, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "CRM - CS Cases", "id": "cs-cases", "url": "http://localhost:8000/crm?tab=cs-cases", "active": false}, "1": {"__type__": "array", "type": "item", "title": "CRM - CS Pending Input", "id": "cs-pending-input", "url": "http://localhost:8000/crm?tab=cs-pending-input", "active": false}, "2": {"__type__": "array", "type": "item", "title": "POMS - Talkdesk", "id": "poms-talkdesk", "url": "http://localhost:8000/crm?tab=poms-talkdesk", "active": false}}}, "2": {"__type__": "array", "type": "section", "title": "ePerolehan", "id": "eper<PERSON>han", "icon": "ki-document", "url": "http://localhost:8000/ep", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "section", "title": "Quotation Tender", "id": "qt", "url": "http://localhost:8000/ep?tab=qt", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Dashboard", "id": "qt-dashboard", "url": "http://localhost:8000/ep?tab=qt-dashboard", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Summary", "id": "summary", "url": "http://localhost:8000/ep?tab=qt-summary", "active": false}}}, "1": {"__type__": "array", "type": "section", "title": "Profile", "id": "profile", "url": "http://localhost:8000/ep?tab=profile", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "MyIdentity", "id": "my-identity", "url": "http://localhost:8000/ep?tab=my-identity", "active": false}, "1": {"__type__": "array", "type": "item", "title": "User Login", "id": "user-login", "url": "http://localhost:8000/ep?tab=user-login", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Find Organization", "id": "find-organization", "url": "http://localhost:8000/ep?tab=organization", "active": false}}}, "2": {"__type__": "array", "type": "section", "title": "Supplier", "id": "supplier", "url": "http://localhost:8000/ep?tab=supplier", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Find Supplier", "id": "find-supplier-technical", "url": "http://localhost:8000/ep?tab=supplier-technical", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Find Supplier", "id": "find-supplier-cs", "url": "http://localhost:8000/ep?tab=supplier-cs", "active": false}}}, "3": {"__type__": "array", "type": "item", "title": "Tracking Diary", "id": "tracking-diary", "url": "http://localhost:8000/ep?tab=tracking-diary", "active": false}, "4": {"__type__": "array", "type": "section", "title": "<PERSON><PERSON>", "id": "item", "url": "http://localhost:8000/ep?tab=item", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Find UOM", "id": "find-uom", "url": "http://localhost:8000/ep?tab=uom", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Find Items", "id": "find-items", "url": "http://localhost:8000/ep?tab=item-code", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Find UNSPSC Items", "id": "find-unspsc-items", "url": "http://localhost:8000/ep?tab=unspsc-item", "active": false}, "3": {"__type__": "array", "type": "item", "title": "Find Supplier's Item", "id": "find-supplier-item", "url": "http://localhost:8000/ep?tab=supplier-item", "active": false}, "4": {"__type__": "array", "type": "item", "title": "Find Item Task History", "id": "item-task-history", "url": "http://localhost:8000/ep?tab=item-history", "active": false}}}}}, "3": {"__type__": "array", "type": "item", "title": "REPORT - REVENUE", "id": "report", "icon": "ki-graph-up", "url": "http://localhost:8000/report", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Today Transaction", "id": "today-trans", "url": "http://localhost:8000/report?tab=today-trans", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Accumulative YTD & MTD", "id": "accumulative", "url": "http://localhost:8000/report?tab=accumulative", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Daily Summary", "id": "daily-summary", "url": "http://localhost:8000/report?tab=daily-summary", "active": false}, "3": {"__type__": "array", "type": "item", "title": "Pending Transaction", "id": "pending-transaction", "url": "http://localhost:8000/report?tab=pending-transaction", "active": false}}}, "4": {"__type__": "array", "type": "item", "title": "BPM", "id": "bpm", "icon": "ki-wrench", "url": "http://localhost:8000/bpm", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Find Task", "id": "find-task", "url": "http://localhost:8000/bpm?tab=find-task", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Instance Query", "id": "instance-query", "url": "http://localhost:8000/bpm?tab=instance-query", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Process Manager", "id": "process-manager", "url": "http://localhost:8000/bpm?tab=process-manager", "active": false}, "3": {"__type__": "array", "type": "item", "title": "Worklist Manager", "id": "worklist-manager", "url": "http://localhost:8000/bpm?tab=worklist-manager", "active": false}, "4": {"__type__": "array", "type": "item", "title": "Service Manager", "id": "service-manager", "url": "http://localhost:8000/bpm?tab=service-manager", "active": false}, "5": {"__type__": "array", "type": "item", "title": "<PERSON><PERSON><PERSON>", "id": "error-handler", "url": "http://localhost:8000/bpm?tab=error-handler", "active": false}, "6": {"__type__": "array", "type": "item", "title": "Track Comp. Instance", "id": "component-instance", "url": "http://localhost:8000/bpm?tab=component-instance", "active": false}}}, "5": {"__type__": "array", "type": "section", "title": "App Settings", "id": "settings", "icon": "ki-setting", "hidden": true, "url": "http://localhost:8000/settings?tab=users", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Users", "id": "users", "url": "http://localhost:8000/settings?tab=users", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Groups", "id": "groups", "url": "http://localhost:8000/settings?tab=groups", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Roles", "id": "roles", "url": "http://localhost:8000/settings?tab=roles", "active": false}, "3": {"__type__": "array", "type": "item", "title": "Permissions", "id": "permissions", "url": "http://localhost:8000/settings?tab=permissions", "active": false}, "4": {"__type__": "array", "type": "item", "title": "Access Control List", "id": "acl", "url": "http://localhost:8000/settings?tab=access-control-list", "active": false}}}}, "secondaryMenuItems": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "CRM", "id": "crm", "icon": "ki-information", "url": "http://localhost:8000/crm", "active": true, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "CRM - CS Cases", "id": "cs-cases", "url": "http://localhost:8000/crm?tab=cs-cases", "active": false}, "1": {"__type__": "array", "type": "item", "title": "CRM - CS Pending Input", "id": "cs-pending-input", "url": "http://localhost:8000/crm?tab=cs-pending-input", "active": false}, "2": {"__type__": "array", "type": "item", "title": "POMS - Talkdesk", "id": "poms-talkdesk", "url": "http://localhost:8000/crm?tab=poms-talkdesk", "active": false}}}}, "user": {"__type__": "array", "first_name": "Super", "last_name": "Admin", "name": "Super Admin", "user_name": "eps<PERSON><PERSON>", "email": "<EMAIL>"}, "currentRoute": "http://localhost:8000/crm", "activeTopLevelItem": "crm", "menuItem": {"__type__": "array", "type": "section", "title": "App Settings", "id": "settings", "icon": "ki-setting", "hidden": true, "url": "http://localhost:8000/settings?tab=users", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Users", "id": "users", "url": "http://localhost:8000/settings?tab=users", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Groups", "id": "groups", "url": "http://localhost:8000/settings?tab=groups", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Roles", "id": "roles", "url": "http://localhost:8000/settings?tab=roles", "active": false}, "3": {"__type__": "array", "type": "item", "title": "Permissions", "id": "permissions", "url": "http://localhost:8000/settings?tab=permissions", "active": false}, "4": {"__type__": "array", "type": "item", "title": "Access Control List", "id": "acl", "url": "http://localhost:8000/settings?tab=access-control-list", "active": false}}}, "loop": null}}}, {"description": "Rendering a view", "start": 1753685652.005717, "end": 1753685652.005717, "duration": 0, "color": null, "data": {"name": "partials.sidebar.item", "data": {"__type__": "array", "app": {"__class__": "Illuminate\\Foundation\\Application", "__omitted__": "blackbox"}, "errors": {"__class__": "Illuminate\\Support\\ViewErrorBag", "*bags": {"__type__": "array", "default": {"__class__": "Illuminate\\Support\\MessageBag", "*messages": {"__type__": "array"}, "*format": ":message"}}}, "_instance": {"__class__": "App\\Livewire\\Component\\Sidebar", "*__id": "RJSgk1CyvvZqXhSHvCpD", "*__name": "component.sidebar", "*listeners": {"__type__": "array"}, "*attributes": {"__class__": "Livewire\\Features\\SupportAttributes\\AttributeCollection", "*items": {"__type__": "array"}, "*escapeWhenCastingToString": false}, "*withValidatorCallback": null, "*rulesFromOutside": {"__type__": "array"}, "*messagesFromOutside": {"__type__": "array"}, "*validationAttributesFromOutside": {"__type__": "array"}, "user": {"__type__": "array", "first_name": "Super", "last_name": "Admin", "name": "Super Admin", "user_name": "eps<PERSON><PERSON>", "email": "<EMAIL>"}, "menuItems": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Home", "id": "home", "icon": "ki-home", "url": "http://localhost:8000/home", "active": false}, "1": {"__type__": "array", "type": "item", "title": "CRM", "id": "crm", "icon": "ki-information", "url": "http://localhost:8000/crm", "active": true, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "CRM - CS Cases", "id": "cs-cases", "url": "http://localhost:8000/crm?tab=cs-cases", "active": false}, "1": {"__type__": "array", "type": "item", "title": "CRM - CS Pending Input", "id": "cs-pending-input", "url": "http://localhost:8000/crm?tab=cs-pending-input", "active": false}, "2": {"__type__": "array", "type": "item", "title": "POMS - Talkdesk", "id": "poms-talkdesk", "url": "http://localhost:8000/crm?tab=poms-talkdesk", "active": false}}}, "2": {"__type__": "array", "type": "section", "title": "ePerolehan", "id": "eper<PERSON>han", "icon": "ki-document", "url": "http://localhost:8000/ep", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "section", "title": "Quotation Tender", "id": "qt", "url": "http://localhost:8000/ep?tab=qt", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Dashboard", "id": "qt-dashboard", "url": "http://localhost:8000/ep?tab=qt-dashboard", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Summary", "id": "summary", "url": "http://localhost:8000/ep?tab=qt-summary", "active": false}}}, "1": {"__type__": "array", "type": "section", "title": "Profile", "id": "profile", "url": "http://localhost:8000/ep?tab=profile", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "MyIdentity", "id": "my-identity", "url": "http://localhost:8000/ep?tab=my-identity", "active": false}, "1": {"__type__": "array", "type": "item", "title": "User Login", "id": "user-login", "url": "http://localhost:8000/ep?tab=user-login", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Find Organization", "id": "find-organization", "url": "http://localhost:8000/ep?tab=organization", "active": false}}}, "2": {"__type__": "array", "type": "section", "title": "Supplier", "id": "supplier", "url": "http://localhost:8000/ep?tab=supplier", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Find Supplier", "id": "find-supplier-technical", "url": "http://localhost:8000/ep?tab=supplier-technical", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Find Supplier", "id": "find-supplier-cs", "url": "http://localhost:8000/ep?tab=supplier-cs", "active": false}}}, "3": {"__type__": "array", "type": "item", "title": "Tracking Diary", "id": "tracking-diary", "url": "http://localhost:8000/ep?tab=tracking-diary", "active": false}, "4": {"__type__": "array", "type": "section", "title": "<PERSON><PERSON>", "id": "item", "url": "http://localhost:8000/ep?tab=item", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Find UOM", "id": "find-uom", "url": "http://localhost:8000/ep?tab=uom", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Find Items", "id": "find-items", "url": "http://localhost:8000/ep?tab=item-code", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Find UNSPSC Items", "id": "find-unspsc-items", "url": "http://localhost:8000/ep?tab=unspsc-item", "active": false}, "3": {"__type__": "array", "type": "item", "title": "Find Supplier's Item", "id": "find-supplier-item", "url": "http://localhost:8000/ep?tab=supplier-item", "active": false}, "4": {"__type__": "array", "type": "item", "title": "Find Item Task History", "id": "item-task-history", "url": "http://localhost:8000/ep?tab=item-history", "active": false}}}}}, "3": {"__type__": "array", "type": "item", "title": "REPORT - REVENUE", "id": "report", "icon": "ki-graph-up", "url": "http://localhost:8000/report", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Today Transaction", "id": "today-trans", "url": "http://localhost:8000/report?tab=today-trans", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Accumulative YTD & MTD", "id": "accumulative", "url": "http://localhost:8000/report?tab=accumulative", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Daily Summary", "id": "daily-summary", "url": "http://localhost:8000/report?tab=daily-summary", "active": false}, "3": {"__type__": "array", "type": "item", "title": "Pending Transaction", "id": "pending-transaction", "url": "http://localhost:8000/report?tab=pending-transaction", "active": false}}}, "4": {"__type__": "array", "type": "item", "title": "BPM", "id": "bpm", "icon": "ki-wrench", "url": "http://localhost:8000/bpm", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Find Task", "id": "find-task", "url": "http://localhost:8000/bpm?tab=find-task", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Instance Query", "id": "instance-query", "url": "http://localhost:8000/bpm?tab=instance-query", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Process Manager", "id": "process-manager", "url": "http://localhost:8000/bpm?tab=process-manager", "active": false}, "3": {"__type__": "array", "type": "item", "title": "Worklist Manager", "id": "worklist-manager", "url": "http://localhost:8000/bpm?tab=worklist-manager", "active": false}, "4": {"__type__": "array", "type": "item", "title": "Service Manager", "id": "service-manager", "url": "http://localhost:8000/bpm?tab=service-manager", "active": false}, "5": {"__type__": "array", "type": "item", "title": "<PERSON><PERSON><PERSON>", "id": "error-handler", "url": "http://localhost:8000/bpm?tab=error-handler", "active": false}, "6": {"__type__": "array", "type": "item", "title": "Track Comp. Instance", "id": "component-instance", "url": "http://localhost:8000/bpm?tab=component-instance", "active": false}}}, "5": {"__type__": "array", "type": "section", "title": "App Settings", "id": "settings", "icon": "ki-setting", "hidden": true, "url": "http://localhost:8000/settings?tab=users", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Users", "id": "users", "url": "http://localhost:8000/settings?tab=users", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Groups", "id": "groups", "url": "http://localhost:8000/settings?tab=groups", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Roles", "id": "roles", "url": "http://localhost:8000/settings?tab=roles", "active": false}, "3": {"__type__": "array", "type": "item", "title": "Permissions", "id": "permissions", "url": "http://localhost:8000/settings?tab=permissions", "active": false}, "4": {"__type__": "array", "type": "item", "title": "Access Control List", "id": "acl", "url": "http://localhost:8000/settings?tab=access-control-list", "active": false}}}}, "currentRoute": "http://localhost:8000/crm", "activeTopLevelItem": "crm"}, "menuItems": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Home", "id": "home", "icon": "ki-home", "url": "http://localhost:8000/home", "active": false}, "1": {"__type__": "array", "type": "item", "title": "CRM", "id": "crm", "icon": "ki-information", "url": "http://localhost:8000/crm", "active": true, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "CRM - CS Cases", "id": "cs-cases", "url": "http://localhost:8000/crm?tab=cs-cases", "active": false}, "1": {"__type__": "array", "type": "item", "title": "CRM - CS Pending Input", "id": "cs-pending-input", "url": "http://localhost:8000/crm?tab=cs-pending-input", "active": false}, "2": {"__type__": "array", "type": "item", "title": "POMS - Talkdesk", "id": "poms-talkdesk", "url": "http://localhost:8000/crm?tab=poms-talkdesk", "active": false}}}, "2": {"__type__": "array", "type": "section", "title": "ePerolehan", "id": "eper<PERSON>han", "icon": "ki-document", "url": "http://localhost:8000/ep", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "section", "title": "Quotation Tender", "id": "qt", "url": "http://localhost:8000/ep?tab=qt", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Dashboard", "id": "qt-dashboard", "url": "http://localhost:8000/ep?tab=qt-dashboard", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Summary", "id": "summary", "url": "http://localhost:8000/ep?tab=qt-summary", "active": false}}}, "1": {"__type__": "array", "type": "section", "title": "Profile", "id": "profile", "url": "http://localhost:8000/ep?tab=profile", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "MyIdentity", "id": "my-identity", "url": "http://localhost:8000/ep?tab=my-identity", "active": false}, "1": {"__type__": "array", "type": "item", "title": "User Login", "id": "user-login", "url": "http://localhost:8000/ep?tab=user-login", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Find Organization", "id": "find-organization", "url": "http://localhost:8000/ep?tab=organization", "active": false}}}, "2": {"__type__": "array", "type": "section", "title": "Supplier", "id": "supplier", "url": "http://localhost:8000/ep?tab=supplier", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Find Supplier", "id": "find-supplier-technical", "url": "http://localhost:8000/ep?tab=supplier-technical", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Find Supplier", "id": "find-supplier-cs", "url": "http://localhost:8000/ep?tab=supplier-cs", "active": false}}}, "3": {"__type__": "array", "type": "item", "title": "Tracking Diary", "id": "tracking-diary", "url": "http://localhost:8000/ep?tab=tracking-diary", "active": false}, "4": {"__type__": "array", "type": "section", "title": "<PERSON><PERSON>", "id": "item", "url": "http://localhost:8000/ep?tab=item", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Find UOM", "id": "find-uom", "url": "http://localhost:8000/ep?tab=uom", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Find Items", "id": "find-items", "url": "http://localhost:8000/ep?tab=item-code", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Find UNSPSC Items", "id": "find-unspsc-items", "url": "http://localhost:8000/ep?tab=unspsc-item", "active": false}, "3": {"__type__": "array", "type": "item", "title": "Find Supplier's Item", "id": "find-supplier-item", "url": "http://localhost:8000/ep?tab=supplier-item", "active": false}, "4": {"__type__": "array", "type": "item", "title": "Find Item Task History", "id": "item-task-history", "url": "http://localhost:8000/ep?tab=item-history", "active": false}}}}}, "3": {"__type__": "array", "type": "item", "title": "REPORT - REVENUE", "id": "report", "icon": "ki-graph-up", "url": "http://localhost:8000/report", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Today Transaction", "id": "today-trans", "url": "http://localhost:8000/report?tab=today-trans", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Accumulative YTD & MTD", "id": "accumulative", "url": "http://localhost:8000/report?tab=accumulative", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Daily Summary", "id": "daily-summary", "url": "http://localhost:8000/report?tab=daily-summary", "active": false}, "3": {"__type__": "array", "type": "item", "title": "Pending Transaction", "id": "pending-transaction", "url": "http://localhost:8000/report?tab=pending-transaction", "active": false}}}, "4": {"__type__": "array", "type": "item", "title": "BPM", "id": "bpm", "icon": "ki-wrench", "url": "http://localhost:8000/bpm", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Find Task", "id": "find-task", "url": "http://localhost:8000/bpm?tab=find-task", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Instance Query", "id": "instance-query", "url": "http://localhost:8000/bpm?tab=instance-query", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Process Manager", "id": "process-manager", "url": "http://localhost:8000/bpm?tab=process-manager", "active": false}, "3": {"__type__": "array", "type": "item", "title": "Worklist Manager", "id": "worklist-manager", "url": "http://localhost:8000/bpm?tab=worklist-manager", "active": false}, "4": {"__type__": "array", "type": "item", "title": "Service Manager", "id": "service-manager", "url": "http://localhost:8000/bpm?tab=service-manager", "active": false}, "5": {"__type__": "array", "type": "item", "title": "<PERSON><PERSON><PERSON>", "id": "error-handler", "url": "http://localhost:8000/bpm?tab=error-handler", "active": false}, "6": {"__type__": "array", "type": "item", "title": "Track Comp. Instance", "id": "component-instance", "url": "http://localhost:8000/bpm?tab=component-instance", "active": false}}}, "5": {"__type__": "array", "type": "section", "title": "App Settings", "id": "settings", "icon": "ki-setting", "hidden": true, "url": "http://localhost:8000/settings?tab=users", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Users", "id": "users", "url": "http://localhost:8000/settings?tab=users", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Groups", "id": "groups", "url": "http://localhost:8000/settings?tab=groups", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Roles", "id": "roles", "url": "http://localhost:8000/settings?tab=roles", "active": false}, "3": {"__type__": "array", "type": "item", "title": "Permissions", "id": "permissions", "url": "http://localhost:8000/settings?tab=permissions", "active": false}, "4": {"__type__": "array", "type": "item", "title": "Access Control List", "id": "acl", "url": "http://localhost:8000/settings?tab=access-control-list", "active": false}}}}, "secondaryMenuItems": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "CRM", "id": "crm", "icon": "ki-information", "url": "http://localhost:8000/crm", "active": true, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "CRM - CS Cases", "id": "cs-cases", "url": "http://localhost:8000/crm?tab=cs-cases", "active": false}, "1": {"__type__": "array", "type": "item", "title": "CRM - CS Pending Input", "id": "cs-pending-input", "url": "http://localhost:8000/crm?tab=cs-pending-input", "active": false}, "2": {"__type__": "array", "type": "item", "title": "POMS - Talkdesk", "id": "poms-talkdesk", "url": "http://localhost:8000/crm?tab=poms-talkdesk", "active": false}}}}, "user": {"__type__": "array", "first_name": "Super", "last_name": "Admin", "name": "Super Admin", "user_name": "eps<PERSON><PERSON>", "email": "<EMAIL>"}, "currentRoute": "http://localhost:8000/crm", "activeTopLevelItem": "crm", "menuItem": {"__type__": "array", "type": "item", "title": "CRM", "id": "crm", "icon": "ki-information", "url": "http://localhost:8000/crm", "active": true, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "CRM - CS Cases", "id": "cs-cases", "url": "http://localhost:8000/crm?tab=cs-cases", "active": false}, "1": {"__type__": "array", "type": "item", "title": "CRM - CS Pending Input", "id": "cs-pending-input", "url": "http://localhost:8000/crm?tab=cs-pending-input", "active": false}, "2": {"__type__": "array", "type": "item", "title": "POMS - Talkdesk", "id": "poms-talkdesk", "url": "http://localhost:8000/crm?tab=poms-talkdesk", "active": false}}}, "loop": {"__class__": "stdClass", "iteration": 1, "index": 0, "remaining": 0, "count": 1, "first": true, "last": true, "odd": true, "even": false, "depth": 1, "parent": null}, "type": "item", "title": "CRM", "id": "crm", "icon": "ki-information", "url": "http://localhost:8000/crm", "active": true, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "CRM - CS Cases", "id": "cs-cases", "url": "http://localhost:8000/crm?tab=cs-cases", "active": false}, "1": {"__type__": "array", "type": "item", "title": "CRM - CS Pending Input", "id": "cs-pending-input", "url": "http://localhost:8000/crm?tab=cs-pending-input", "active": false}, "2": {"__type__": "array", "type": "item", "title": "POMS - Talkdesk", "id": "poms-talkdesk", "url": "http://localhost:8000/crm?tab=poms-talkdesk", "active": false}}}}}, {"description": "Rendering a view", "start": 1753685652.009676, "end": 1753685652.009676, "duration": 0, "color": null, "data": {"name": "partials.sidebar.simple-item", "data": {"__type__": "array", "app": {"__class__": "Illuminate\\Foundation\\Application", "__omitted__": "blackbox"}, "errors": {"__class__": "Illuminate\\Support\\ViewErrorBag", "*bags": {"__type__": "array", "default": {"__class__": "Illuminate\\Support\\MessageBag", "*messages": {"__type__": "array"}, "*format": ":message"}}}, "_instance": {"__class__": "App\\Livewire\\Component\\Sidebar", "*__id": "RJSgk1CyvvZqXhSHvCpD", "*__name": "component.sidebar", "*listeners": {"__type__": "array"}, "*attributes": {"__class__": "Livewire\\Features\\SupportAttributes\\AttributeCollection", "*items": {"__type__": "array"}, "*escapeWhenCastingToString": false}, "*withValidatorCallback": null, "*rulesFromOutside": {"__type__": "array"}, "*messagesFromOutside": {"__type__": "array"}, "*validationAttributesFromOutside": {"__type__": "array"}, "user": {"__type__": "array", "first_name": "Super", "last_name": "Admin", "name": "Super Admin", "user_name": "eps<PERSON><PERSON>", "email": "<EMAIL>"}, "menuItems": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Home", "id": "home", "icon": "ki-home", "url": "http://localhost:8000/home", "active": false}, "1": {"__type__": "array", "type": "item", "title": "CRM", "id": "crm", "icon": "ki-information", "url": "http://localhost:8000/crm", "active": true, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "CRM - CS Cases", "id": "cs-cases", "url": "http://localhost:8000/crm?tab=cs-cases", "active": false}, "1": {"__type__": "array", "type": "item", "title": "CRM - CS Pending Input", "id": "cs-pending-input", "url": "http://localhost:8000/crm?tab=cs-pending-input", "active": false}, "2": {"__type__": "array", "type": "item", "title": "POMS - Talkdesk", "id": "poms-talkdesk", "url": "http://localhost:8000/crm?tab=poms-talkdesk", "active": false}}}, "2": {"__type__": "array", "type": "section", "title": "ePerolehan", "id": "eper<PERSON>han", "icon": "ki-document", "url": "http://localhost:8000/ep", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "section", "title": "Quotation Tender", "id": "qt", "url": "http://localhost:8000/ep?tab=qt", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Dashboard", "id": "qt-dashboard", "url": "http://localhost:8000/ep?tab=qt-dashboard", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Summary", "id": "summary", "url": "http://localhost:8000/ep?tab=qt-summary", "active": false}}}, "1": {"__type__": "array", "type": "section", "title": "Profile", "id": "profile", "url": "http://localhost:8000/ep?tab=profile", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "MyIdentity", "id": "my-identity", "url": "http://localhost:8000/ep?tab=my-identity", "active": false}, "1": {"__type__": "array", "type": "item", "title": "User Login", "id": "user-login", "url": "http://localhost:8000/ep?tab=user-login", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Find Organization", "id": "find-organization", "url": "http://localhost:8000/ep?tab=organization", "active": false}}}, "2": {"__type__": "array", "type": "section", "title": "Supplier", "id": "supplier", "url": "http://localhost:8000/ep?tab=supplier", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Find Supplier", "id": "find-supplier-technical", "url": "http://localhost:8000/ep?tab=supplier-technical", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Find Supplier", "id": "find-supplier-cs", "url": "http://localhost:8000/ep?tab=supplier-cs", "active": false}}}, "3": {"__type__": "array", "type": "item", "title": "Tracking Diary", "id": "tracking-diary", "url": "http://localhost:8000/ep?tab=tracking-diary", "active": false}, "4": {"__type__": "array", "type": "section", "title": "<PERSON><PERSON>", "id": "item", "url": "http://localhost:8000/ep?tab=item", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Find UOM", "id": "find-uom", "url": "http://localhost:8000/ep?tab=uom", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Find Items", "id": "find-items", "url": "http://localhost:8000/ep?tab=item-code", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Find UNSPSC Items", "id": "find-unspsc-items", "url": "http://localhost:8000/ep?tab=unspsc-item", "active": false}, "3": {"__type__": "array", "type": "item", "title": "Find Supplier's Item", "id": "find-supplier-item", "url": "http://localhost:8000/ep?tab=supplier-item", "active": false}, "4": {"__type__": "array", "type": "item", "title": "Find Item Task History", "id": "item-task-history", "url": "http://localhost:8000/ep?tab=item-history", "active": false}}}}}, "3": {"__type__": "array", "type": "item", "title": "REPORT - REVENUE", "id": "report", "icon": "ki-graph-up", "url": "http://localhost:8000/report", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Today Transaction", "id": "today-trans", "url": "http://localhost:8000/report?tab=today-trans", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Accumulative YTD & MTD", "id": "accumulative", "url": "http://localhost:8000/report?tab=accumulative", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Daily Summary", "id": "daily-summary", "url": "http://localhost:8000/report?tab=daily-summary", "active": false}, "3": {"__type__": "array", "type": "item", "title": "Pending Transaction", "id": "pending-transaction", "url": "http://localhost:8000/report?tab=pending-transaction", "active": false}}}, "4": {"__type__": "array", "type": "item", "title": "BPM", "id": "bpm", "icon": "ki-wrench", "url": "http://localhost:8000/bpm", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Find Task", "id": "find-task", "url": "http://localhost:8000/bpm?tab=find-task", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Instance Query", "id": "instance-query", "url": "http://localhost:8000/bpm?tab=instance-query", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Process Manager", "id": "process-manager", "url": "http://localhost:8000/bpm?tab=process-manager", "active": false}, "3": {"__type__": "array", "type": "item", "title": "Worklist Manager", "id": "worklist-manager", "url": "http://localhost:8000/bpm?tab=worklist-manager", "active": false}, "4": {"__type__": "array", "type": "item", "title": "Service Manager", "id": "service-manager", "url": "http://localhost:8000/bpm?tab=service-manager", "active": false}, "5": {"__type__": "array", "type": "item", "title": "<PERSON><PERSON><PERSON>", "id": "error-handler", "url": "http://localhost:8000/bpm?tab=error-handler", "active": false}, "6": {"__type__": "array", "type": "item", "title": "Track Comp. Instance", "id": "component-instance", "url": "http://localhost:8000/bpm?tab=component-instance", "active": false}}}, "5": {"__type__": "array", "type": "section", "title": "App Settings", "id": "settings", "icon": "ki-setting", "hidden": true, "url": "http://localhost:8000/settings?tab=users", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Users", "id": "users", "url": "http://localhost:8000/settings?tab=users", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Groups", "id": "groups", "url": "http://localhost:8000/settings?tab=groups", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Roles", "id": "roles", "url": "http://localhost:8000/settings?tab=roles", "active": false}, "3": {"__type__": "array", "type": "item", "title": "Permissions", "id": "permissions", "url": "http://localhost:8000/settings?tab=permissions", "active": false}, "4": {"__type__": "array", "type": "item", "title": "Access Control List", "id": "acl", "url": "http://localhost:8000/settings?tab=access-control-list", "active": false}}}}, "currentRoute": "http://localhost:8000/crm", "activeTopLevelItem": "crm"}, "menuItems": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Home", "id": "home", "icon": "ki-home", "url": "http://localhost:8000/home", "active": false}, "1": {"__type__": "array", "type": "item", "title": "CRM", "id": "crm", "icon": "ki-information", "url": "http://localhost:8000/crm", "active": true, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "CRM - CS Cases", "id": "cs-cases", "url": "http://localhost:8000/crm?tab=cs-cases", "active": false}, "1": {"__type__": "array", "type": "item", "title": "CRM - CS Pending Input", "id": "cs-pending-input", "url": "http://localhost:8000/crm?tab=cs-pending-input", "active": false}, "2": {"__type__": "array", "type": "item", "title": "POMS - Talkdesk", "id": "poms-talkdesk", "url": "http://localhost:8000/crm?tab=poms-talkdesk", "active": false}}}, "2": {"__type__": "array", "type": "section", "title": "ePerolehan", "id": "eper<PERSON>han", "icon": "ki-document", "url": "http://localhost:8000/ep", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "section", "title": "Quotation Tender", "id": "qt", "url": "http://localhost:8000/ep?tab=qt", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Dashboard", "id": "qt-dashboard", "url": "http://localhost:8000/ep?tab=qt-dashboard", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Summary", "id": "summary", "url": "http://localhost:8000/ep?tab=qt-summary", "active": false}}}, "1": {"__type__": "array", "type": "section", "title": "Profile", "id": "profile", "url": "http://localhost:8000/ep?tab=profile", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "MyIdentity", "id": "my-identity", "url": "http://localhost:8000/ep?tab=my-identity", "active": false}, "1": {"__type__": "array", "type": "item", "title": "User Login", "id": "user-login", "url": "http://localhost:8000/ep?tab=user-login", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Find Organization", "id": "find-organization", "url": "http://localhost:8000/ep?tab=organization", "active": false}}}, "2": {"__type__": "array", "type": "section", "title": "Supplier", "id": "supplier", "url": "http://localhost:8000/ep?tab=supplier", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Find Supplier", "id": "find-supplier-technical", "url": "http://localhost:8000/ep?tab=supplier-technical", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Find Supplier", "id": "find-supplier-cs", "url": "http://localhost:8000/ep?tab=supplier-cs", "active": false}}}, "3": {"__type__": "array", "type": "item", "title": "Tracking Diary", "id": "tracking-diary", "url": "http://localhost:8000/ep?tab=tracking-diary", "active": false}, "4": {"__type__": "array", "type": "section", "title": "<PERSON><PERSON>", "id": "item", "url": "http://localhost:8000/ep?tab=item", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Find UOM", "id": "find-uom", "url": "http://localhost:8000/ep?tab=uom", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Find Items", "id": "find-items", "url": "http://localhost:8000/ep?tab=item-code", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Find UNSPSC Items", "id": "find-unspsc-items", "url": "http://localhost:8000/ep?tab=unspsc-item", "active": false}, "3": {"__type__": "array", "type": "item", "title": "Find Supplier's Item", "id": "find-supplier-item", "url": "http://localhost:8000/ep?tab=supplier-item", "active": false}, "4": {"__type__": "array", "type": "item", "title": "Find Item Task History", "id": "item-task-history", "url": "http://localhost:8000/ep?tab=item-history", "active": false}}}}}, "3": {"__type__": "array", "type": "item", "title": "REPORT - REVENUE", "id": "report", "icon": "ki-graph-up", "url": "http://localhost:8000/report", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Today Transaction", "id": "today-trans", "url": "http://localhost:8000/report?tab=today-trans", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Accumulative YTD & MTD", "id": "accumulative", "url": "http://localhost:8000/report?tab=accumulative", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Daily Summary", "id": "daily-summary", "url": "http://localhost:8000/report?tab=daily-summary", "active": false}, "3": {"__type__": "array", "type": "item", "title": "Pending Transaction", "id": "pending-transaction", "url": "http://localhost:8000/report?tab=pending-transaction", "active": false}}}, "4": {"__type__": "array", "type": "item", "title": "BPM", "id": "bpm", "icon": "ki-wrench", "url": "http://localhost:8000/bpm", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Find Task", "id": "find-task", "url": "http://localhost:8000/bpm?tab=find-task", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Instance Query", "id": "instance-query", "url": "http://localhost:8000/bpm?tab=instance-query", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Process Manager", "id": "process-manager", "url": "http://localhost:8000/bpm?tab=process-manager", "active": false}, "3": {"__type__": "array", "type": "item", "title": "Worklist Manager", "id": "worklist-manager", "url": "http://localhost:8000/bpm?tab=worklist-manager", "active": false}, "4": {"__type__": "array", "type": "item", "title": "Service Manager", "id": "service-manager", "url": "http://localhost:8000/bpm?tab=service-manager", "active": false}, "5": {"__type__": "array", "type": "item", "title": "<PERSON><PERSON><PERSON>", "id": "error-handler", "url": "http://localhost:8000/bpm?tab=error-handler", "active": false}, "6": {"__type__": "array", "type": "item", "title": "Track Comp. Instance", "id": "component-instance", "url": "http://localhost:8000/bpm?tab=component-instance", "active": false}}}, "5": {"__type__": "array", "type": "section", "title": "App Settings", "id": "settings", "icon": "ki-setting", "hidden": true, "url": "http://localhost:8000/settings?tab=users", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Users", "id": "users", "url": "http://localhost:8000/settings?tab=users", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Groups", "id": "groups", "url": "http://localhost:8000/settings?tab=groups", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Roles", "id": "roles", "url": "http://localhost:8000/settings?tab=roles", "active": false}, "3": {"__type__": "array", "type": "item", "title": "Permissions", "id": "permissions", "url": "http://localhost:8000/settings?tab=permissions", "active": false}, "4": {"__type__": "array", "type": "item", "title": "Access Control List", "id": "acl", "url": "http://localhost:8000/settings?tab=access-control-list", "active": false}}}}, "secondaryMenuItems": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "CRM", "id": "crm", "icon": "ki-information", "url": "http://localhost:8000/crm", "active": true, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "CRM - CS Cases", "id": "cs-cases", "url": "http://localhost:8000/crm?tab=cs-cases", "active": false}, "1": {"__type__": "array", "type": "item", "title": "CRM - CS Pending Input", "id": "cs-pending-input", "url": "http://localhost:8000/crm?tab=cs-pending-input", "active": false}, "2": {"__type__": "array", "type": "item", "title": "POMS - Talkdesk", "id": "poms-talkdesk", "url": "http://localhost:8000/crm?tab=poms-talkdesk", "active": false}}}}, "user": {"__type__": "array", "first_name": "Super", "last_name": "Admin", "name": "Super Admin", "user_name": "eps<PERSON><PERSON>", "email": "<EMAIL>"}, "currentRoute": "http://localhost:8000/crm", "activeTopLevelItem": "crm", "menuItem": {"__type__": "array", "type": "item", "title": "CRM", "id": "crm", "icon": "ki-information", "url": "http://localhost:8000/crm", "active": true, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "CRM - CS Cases", "id": "cs-cases", "url": "http://localhost:8000/crm?tab=cs-cases", "active": false}, "1": {"__type__": "array", "type": "item", "title": "CRM - CS Pending Input", "id": "cs-pending-input", "url": "http://localhost:8000/crm?tab=cs-pending-input", "active": false}, "2": {"__type__": "array", "type": "item", "title": "POMS - Talkdesk", "id": "poms-talkdesk", "url": "http://localhost:8000/crm?tab=poms-talkdesk", "active": false}}}, "loop": {"__class__": "stdClass", "iteration": 1, "index": 0, "remaining": 2, "count": 3, "first": true, "last": false, "odd": true, "even": false, "depth": 2, "parent": {"__class__": "stdClass", "iteration": 1, "index": 0, "remaining": 0, "count": 1, "first": true, "last": true, "odd": true, "even": false, "depth": 1, "parent": null}}, "type": "item", "title": "CRM - CS Cases", "id": "crm", "icon": "ki-information", "url": "http://localhost:8000/crm?tab=cs-cases", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "CRM - CS Cases", "id": "cs-cases", "url": "http://localhost:8000/crm?tab=cs-cases", "active": false}, "1": {"__type__": "array", "type": "item", "title": "CRM - CS Pending Input", "id": "cs-pending-input", "url": "http://localhost:8000/crm?tab=cs-pending-input", "active": false}, "2": {"__type__": "array", "type": "item", "title": "POMS - Talkdesk", "id": "poms-talkdesk", "url": "http://localhost:8000/crm?tab=poms-talkdesk", "active": false}}, "level": 2, "linkPadding": "ps-2.5", "levelClasses": "", "item": {"__type__": "array", "type": "item", "title": "CRM - CS Cases", "id": "cs-cases", "url": "http://localhost:8000/crm?tab=cs-cases", "active": false}, "disabled": false, "badge": null}}}, {"description": "Rendering a view", "start": 1753685652.02176, "end": 1753685652.02176, "duration": 0, "color": null, "data": {"name": "livewire.component.header", "data": {"__type__": "array", "user": {"__type__": "array", "first_name": "Super", "last_name": "Admin", "name": "Super Admin", "user_name": "eps<PERSON><PERSON>", "email": "<EMAIL>"}, "breadcrumbs": {"__type__": "array", "0": {"__type__": "array", "title": "CRM", "is_last": true, "route": "crm", "query": null}}}}}, {"description": "Rendering a view", "start": 1753685652.024032, "end": 1753685652.024032, "duration": 0, "color": null, "data": {"name": "partials.header.notification-menu", "data": {"__type__": "array", "app": {"__class__": "Illuminate\\Foundation\\Application", "__omitted__": "blackbox"}, "errors": {"__class__": "Illuminate\\Support\\ViewErrorBag", "*bags": {"__type__": "array", "default": {"__class__": "Illuminate\\Support\\MessageBag", "*messages": {"__type__": "array"}, "*format": ":message"}}}, "_instance": {"__class__": "App\\Livewire\\Component\\Header", "*__id": "alC9BmrTdnIo9khJqa6x", "*__name": "component.header", "*listeners": {"__type__": "array"}, "*attributes": {"__class__": "Livewire\\Features\\SupportAttributes\\AttributeCollection", "*items": {"__type__": "array"}, "*escapeWhenCastingToString": false}, "*withValidatorCallback": null, "*rulesFromOutside": {"__type__": "array"}, "*messagesFromOutside": {"__type__": "array"}, "*validationAttributesFromOutside": {"__type__": "array"}, "user": {"__type__": "array", "first_name": "Super", "last_name": "Admin", "name": "Super Admin", "user_name": "eps<PERSON><PERSON>", "email": "<EMAIL>"}, "breadcrumbs": {"__type__": "array", "0": {"__type__": "array", "title": "CRM", "is_last": true, "route": "crm", "query": null}}}, "user": {"__type__": "array", "first_name": "Super", "last_name": "Admin", "name": "Super Admin", "user_name": "eps<PERSON><PERSON>", "email": "<EMAIL>"}, "breadcrumbs": {"__type__": "array", "0": {"__type__": "array", "title": "CRM", "is_last": true, "route": "crm", "query": null}}, "breadcrumb": {"__type__": "array", "title": "CRM", "is_last": true, "route": "crm", "query": null}, "index": 0, "loop": null}}}, {"description": "Rendering a view", "start": 1753685652.028096, "end": 1753685652.028096, "duration": 0, "color": null, "data": {"name": "components.footer", "data": {"__type__": "array", "app": {"__class__": "Illuminate\\Foundation\\Application", "__omitted__": "blackbox"}, "errors": {"__class__": "Illuminate\\Support\\ViewErrorBag", "*bags": {"__type__": "array"}}, "attributes": {"__class__": "Illuminate\\View\\ComponentAttributeBag", "*attributes": {"__type__": "array"}}, "slot": {"__class__": "Illuminate\\View\\ComponentSlot", "attributes": {"__class__": "Illuminate\\View\\ComponentAttributeBag", "*attributes": {"__type__": "array"}}, "*contents": "<div wire:snapshot=\"{&quot;data&quot;:{&quot;activeTab&quot;:null},&quot;memo&quot;:{&quot;id&quot;:&quot;sReYd0IfiqXQRlg9p74z&quot;,&quot;name&quot;:&quot;project.c-r-m&quot;,&quot;path&quot;:&quot;crm&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;children&quot;:[],&quot;scripts&quot;:[],&quot;assets&quot;:[],&quot;errors&quot;:[],&quot;locale&quot;:&quot;en&quot;},&quot;checksum&quot;:&quot;a0886d53d7d21eb24354852ecf8c665acd523295f2d0b42f2598dbdc7b0705df&quot;}\" wire:effects=\"[]\" wire:id=\"sReYd0IfiqXQRlg9p74z\">\n    <div class=\"d-flex flex-column flex-lg-row\">\n        <!-- Tabs navigation -->\n        <div class=\"d-flex flex-column flex-lg-row gap-5 gap-lg-0\">\n            <!-- Dynamic component container -->\n            <div class=\"tab-content flex-grow-1\">\n                <!--[if BLOCK]><![endif]--><!--[if ENDBLOCK]><![endif]-->\n            </div>\n        </div>\n    </div>\n</div>"}}}}], "userData": [], "httpRequests": [], "subrequests": [], "xdebug": [], "commandName": null, "commandArguments": [], "commandArgumentsDefaults": [], "commandOptions": [], "commandOptionsDefaults": [], "commandExitCode": null, "commandOutput": null, "jobName": null, "jobDescription": null, "jobStatus": null, "jobPayload": [], "jobQueue": null, "jobConnection": null, "jobOptions": [], "testName": null, "testStatus": null, "testStatusMessage": null, "testAsserts": [], "clientMetrics": [], "webVitals": [], "parent": null, "updateToken": "dafa4f6e"}