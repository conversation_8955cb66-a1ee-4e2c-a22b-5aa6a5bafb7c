<?php
    // Set default level if not passed
    $level = $level ?? 1;
    // Calculate padding/styles based on level
    $linkPadding = 'ps-' . (2.5 + ($level - 1) * 4);

    // Apply different styles based on nesting level
    $levelClasses = '';
    if ($level > 1) {
        $levelClasses = 'ms-' . (($level - 1) * 2) . ' border-s border-s-gray-200 ps-3';
    }
?>


<div class="menu-item <?php echo e($active ?? false ? 'here show' : ''); ?> <?php echo e($levelClasses); ?>" data-menu-item-toggle="accordion"
    data-menu-item-trigger="click">
    
    <div class="menu-link py-2 <?php echo e($linkPadding); ?> pe-2.5 rounded-md border border-transparent">
        <!--[if BLOCK]><![endif]--><?php if($level > 1): ?>
            <span class="menu-bullet me-2">
                <span class="bullet bullet-dot size-2 bg-gray-500"></span>
            </span>
        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
        <span
            class="menu-title text-2sm text-gray-800 menu-item-here:text-gray-900 menu-item-show:text-gray-900 menu-link-hover:text-gray-900 <?php echo e(($active ?? false) ? 'font-semibold text-gray-900' : ''); ?>">
            <?php echo e($title); ?>

        </span>
        <span class="menu-arrow text-gray-500">
            <i class="ki-filled ki-down text-3xs menu-item-show:hidden"></i>
            <i class="ki-filled ki-up text-3xs hidden menu-item-show:inline-flex"></i>
        </span>
    </div>

    
    <div class="menu-accordion gap-px">
        <?php $__currentLoopData = $items; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <!--[if BLOCK]><![endif]--><?php if(isset($item['items']) && count($item['items']) > 0): ?>
                
                <?php echo $__env->make('partials.sidebar.item', [
                    'title' => $item['title'],
                    'items' => $item['items'],
                    'active' => $item['active'] ?? false,
                    'level' => $level + 1
                ], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
            <?php else: ?>
                    
                    <?php echo $__env->make('partials.sidebar.simple-item', [
                        'title' => $item['title'],
                        'url' => $item['url'] ?? '#',
                        'active' => $item['active'] ?? false,
                        'disabled' => $item['disabled'] ?? false,
                        'badge' => $item['badge'] ?? null,
                        'level' => $level + 1
                    ], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->

        
        <!--[if BLOCK]><![endif]--><?php if(isset($showMore) && $showMore && isset($hiddenItems)): ?>
            <?php echo $__env->make('partials.sidebar.show-more', ['hiddenItems' => $hiddenItems, 'level' => $level], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
    </div>
</div><?php /**PATH E:\workspace\epss-revamp\resources\views/partials/sidebar/item.blade.php ENDPATH**/ ?>