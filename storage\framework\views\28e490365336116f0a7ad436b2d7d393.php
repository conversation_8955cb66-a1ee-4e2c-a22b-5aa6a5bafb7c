<div class="menu-dropdown menu-default light:border-gray-300 w-screen max-w-[320px]">
    
    <div class="flex items-center justify-between px-5 py-1.5 gap-1.5">
        <div class="flex items-center gap-2">
            <img alt="" class="size-9 rounded-full border-2 border-success"
                src="<?php echo e(asset('assets/media/avatars/1024-1.png')); ?>"> 
            <div class="flex flex-col gap-1.5">
                <span class="text-sm text-gray-800 font-semibold leading-none">
                    <?php echo e($user['first_name'] ?? 'User Name'); ?>

                </span>
                <a class="text-xs text-gray-600 hover:text-primary font-medium leading-none"
                    href="<?php echo e(route('profile')); ?>"> 
                    <?php echo e($user['email'] ?? '<EMAIL>'); ?>

                </a>
            </div>
        </div>
        
        
    </div>
    <div class="menu-separator"></div>

    
    <div class="flex flex-col">

        <div class="menu-item">
            <a class="menu-link" href="<?php echo e(route('settings') . '?tab=users'); ?>">
                <span class="menu-icon"><i class="ki-filled ki-setting"></i></span>
                <span class="menu-title">App Settings</span>
            </a>
        </div>

        <div class="menu-item" data-menu-item-offset="-50px, 0" data-menu-item-placement="left-start"
            data-menu-item-placement-rtl="right-start" data-menu-item-toggle="dropdown"
            data-menu-item-trigger="click|lg:hover">
            <div class="menu-link">
                <span class="menu-icon"><i class="ki-filled ki-setting-2"></i></span>
                <span class="menu-title">My Account</span>
                <span class="menu-arrow"><i class="ki-filled ki-right text-3xs rtl:transform rtl:rotate-180"></i></span>
            </div>
            <div class="menu-dropdown menu-default light:border-gray-300 w-full max-w-[290px]">
                <div class="menu-item">
                    <a class="menu-link" href="<?php echo e(route('profile')); ?>">
                        <span class="menu-icon"><i class="ki-filled ki-profile-circle"></i></span>
                        <span class="menu-title">My Profile</span>
                    </a>
                </div>
                <!-- Add account-related links below as needed -->
            </div>
        </div>
    </div>

    <div class="menu-separator"></div>

    <div class="flex flex-col">
        <!-- Dark Mode Toggle -->
        <div class="menu-item mb-0.5">
            <div class="menu-link">
                <span class="menu-icon"><i class="ki-filled ki-moon"></i></span>
                <span class="menu-title">Dark Mode</span>
                <label class="switch switch-sm">
                    <input data-theme-state="dark" data-theme-toggle="true" name="check" type="checkbox" value="1">
                </label>
            </div>
        </div>
        <!-- Logout Button -->
        <div class="menu-item px-4 py-1.5">
            <button wire:click="logout" class="btn btn-sm btn-light justify-center w-full">
                Log out
            </button>
        </div>
    </div>
</div><?php /**PATH E:\workspace\epss-revamp\resources\views/partials/sidebar/user-menu.blade.php ENDPATH**/ ?>