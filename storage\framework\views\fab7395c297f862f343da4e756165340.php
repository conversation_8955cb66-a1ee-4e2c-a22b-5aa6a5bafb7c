<div>
    <div class="d-flex flex-column flex-lg-row">
        <!-- Tabs navigation -->
        <div class="d-flex flex-column flex-lg-row gap-5 gap-lg-0">
            <!-- Dynamic component container -->
            <div class="tab-content flex-grow-1">
                <!--[if BLOCK]><![endif]--><?php if($activeTab === 'cs-cases'): ?>
                    <?php
$__split = function ($name, $params = []) {
    return [$name, $params];
};
[$__name, $__params] = $__split('module.crm.crm-cs-cases');

$__html = app('livewire')->mount($__name, $__params, 'lw-2568938365-0', $__slots ?? [], get_defined_vars());

echo $__html;

unset($__html);
unset($__name);
unset($__params);
unset($__split);
if (isset($__slots)) unset($__slots);
?>
                <?php elseif($activeTab === 'cs-pending-input'): ?>
                    <?php
$__split = function ($name, $params = []) {
    return [$name, $params];
};
[$__name, $__params] = $__split('module.crm.crm-cs-pending-input');

$__html = app('livewire')->mount($__name, $__params, 'lw-2568938365-1', $__slots ?? [], get_defined_vars());

echo $__html;

unset($__html);
unset($__name);
unset($__params);
unset($__split);
if (isset($__slots)) unset($__slots);
?>
                <?php elseif($activeTab === 'poms-talkdesk'): ?>
                    <?php
$__split = function ($name, $params = []) {
    return [$name, $params];
};
[$__name, $__params] = $__split('module.crm.poms-talkdesk');

$__html = app('livewire')->mount($__name, $__params, 'lw-2568938365-2', $__slots ?? [], get_defined_vars());

echo $__html;

unset($__html);
unset($__name);
unset($__params);
unset($__split);
if (isset($__slots)) unset($__slots);
?>
                <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
            </div>
        </div>
    </div>
</div><?php /**PATH E:\workspace\epss-revamp\resources\views/livewire/project/crm/index.blade.php ENDPATH**/ ?>