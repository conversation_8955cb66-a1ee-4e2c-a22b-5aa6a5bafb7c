<!DOCTYPE html>
<html class="h-full" data-theme="true" data-theme-mode="light" dir="ltr" lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>EPSS 2.0</title>
    <meta content="follow, index" name="robots" />
    <meta content="width=device-width, initial-scale=1, shrink-to-fit=no" name="viewport" />
    <meta content="en_US" property="og:locale" />
    <meta content="website" property="og:type" />
    <meta content="@keenthemes" property="og:site_name" />
    <meta content="Metronic - Tailwind CSS " property="og:title" />
    <meta content="" property="og:description" />
    <meta content="assets/media/app/og-image.png" property="og:image" />
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>" />

    <!-- Favicons -->
    <link href="<?php echo e(asset('assets/media/app/apple-touch-icon.png')); ?>" rel="apple-touch-icon" sizes="180x180" />
    <link href="<?php echo e(asset('assets/media/app/favicon-32x32.png')); ?>" rel="icon" sizes="32x32" type="image/png" />
    <link href="<?php echo e(asset('assets/media/app/favicon-16x16.png')); ?>" rel="icon" sizes="16x16" type="image/png" />
    <link href="<?php echo e(asset('assets/media/app/favicon.ico')); ?>" rel="shortcut icon" />

    <?php echo app('Illuminate\Foundation\Vite')('resources/css/app.scss'); ?>

    <!-- Fonts & Styles -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet" />
    <link href="<?php echo e(asset('assets/vendors/apexcharts/apexcharts.css')); ?>" rel="stylesheet" />
    <link href="<?php echo e(asset('assets/vendors/keenicons/styles.bundle.css')); ?>" rel="stylesheet" />
    <link href="<?php echo e(asset('assets/css/styles.css')); ?>" rel="stylesheet" />

    <?php echo \Livewire\Mechanisms\FrontendAssets\FrontendAssets::styles(); ?>

</head>

<body
    class="antialiased flex h-full text-base text-gray-700 [--tw-page-bg:#F6F6F9] [--tw-page-bg-dark:var(--tw-coal-200)] [--tw-content-bg:var(--tw-light)] [--tw-content-bg-dark:var(--tw-coal-500)] [--tw-content-scrollbar-color:#e8e8e8] [--tw-header-height:60px] [--tw-sidebar-width:290px] [--tw-sidebar-collapsed-width:70px] bg-[--tw-page-bg] dark:bg-[--tw-page-bg-dark] lg:overflow-hidden">
    <script>
        // Theme mode handling
        const defaultThemeMode = 'light';
        let themeMode;
        if (document.documentElement) {
            if (localStorage.getItem('theme')) {
                themeMode = localStorage.getItem('theme');
            } else if (document.documentElement.hasAttribute('data-theme-mode')) {
                themeMode = document.documentElement.getAttribute('data-theme-mode');
            } else {
                themeMode = defaultThemeMode;
            }
            if (themeMode === 'system') {
                themeMode = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
            }
            document.documentElement.classList.add(themeMode);
        }
    </script>
    <div class="flex grow">
        <!-- Mobile Header -->
        <header
            class="flex lg:hidden items-center fixed z-10 top-0 start-0 end-0 shrink-0 bg-[--tw-page-bg] dark:bg-[--tw-page-bg-dark] h-[--tw-header-height]"
            id="header_sm">
            <div class="container-fluid flex items-center justify-between flex-wrap gap-3">
                <a href="/">
                    <img class="dark:hidden min-h-[30px]" src="<?php echo e(asset('assets/media/app/epss-logo.svg')); ?>" />
                    <img class="hidden dark:block min-h-[30px]"
                        src="<?php echo e(asset('assets/media/app/epss-logo-dark.svg')); ?>" />
                </a>
                <button class="btn btn-icon btn-light btn-clear btn-sm -me-2" data-drawer-toggle="#sidebar">
                    <i class="ki-filled ki-menu"></i>
                </button>
            </div>
        </header>
        <div class="flex flex-col lg:flex-row grow pt-[--tw-header-height] lg:pt-0">
            <?php
$__split = function ($name, $params = []) {
    return [$name, $params];
};
[$__name, $__params] = $__split('component.sidebar', []);

$__html = app('livewire')->mount($__name, $__params, 'lw-3507667110-0', $__slots ?? [], get_defined_vars());

echo $__html;

unset($__html);
unset($__name);
unset($__params);
unset($__split);
if (isset($__slots)) unset($__slots);
?>
            <div class="flex grow rounded-xl bg-[--tw-content-bg] dark:bg-[--tw-content-bg-dark] border border-gray-300 dark:border-gray-200 transition-all duration-300 ease-in-out mt-0 lg:mt-5 mr-5 mb-5 ml-5 lg:ml-0"
                id="main-content">
                <div class="flex flex-col grow lg:scrollable-y-auto lg:[scrollbar-width:auto] lg:light:[--tw-scrollbar-thumb-color:var(--tw-content-scrollbar-color)] pt-5"
                    id="scrollable_content">
                    <?php
$__split = function ($name, $params = []) {
    return [$name, $params];
};
[$__name, $__params] = $__split('component.header', []);

$__html = app('livewire')->mount($__name, $__params, 'lw-3507667110-1', $__slots ?? [], get_defined_vars());

echo $__html;

unset($__html);
unset($__name);
unset($__params);
unset($__split);
if (isset($__slots)) unset($__slots);
?>
                    <main class="grow" role="content">
                        <div class="container-fluid">
                            <?php echo e($slot); ?>

                        </div>
                    </main>
                    <?php echo $__env->make('components.footer', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                </div>
            </div>
        </div>
    </div>
    <!-- Search Modal -->
    <div class="modal" data-modal="true" id="search_modal">
        <div class="modal-content max-w-[600px] top-[15%]">
            <div class="modal-header py-4 px-5">
                <i class="ki-filled ki-magnifier text-gray-700 text-xl"></i>
                <input class="input px-0 border-none bg-transparent shadow-none ms-2.5" name="query"
                    placeholder="Tap to start search" type="text" value="" />
                <button class="btn btn-sm btn-icon btn-light btn-clear shrink-0" data-modal-dismiss="true">
                    <i class="ki-filled ki-cross"></i>
                </button>
            </div>
            <div class="modal-body p-0 pb-5">
                <!-- Extend modal content as needed -->
                <div class="scrollable-y-auto" data-scrollable="true" data-scrollable-max-height="auto"
                    data-scrollable-offset="300px">
                    <div class="flex flex-col text-center py-9 gap-5">
                        Search content goes here...
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- Add other modals (share_profile_modal, report_user_modal, etc.) if needed -->
    <?php echo \Livewire\Mechanisms\FrontendAssets\FrontendAssets::scripts(); ?>

    <script src="<?php echo e(asset('assets/js/core.bundle.js')); ?>"></script>
    <script src="<?php echo e(asset('assets/vendors/apexcharts/apexcharts.min.js')); ?>"></script>
    <script src="<?php echo e(asset('assets/js/widgets/general.js')); ?>"></script>
    <script>
        // Initialize on Livewire navigation
        document.addEventListener('livewire:navigated', function () {
            initializeSidebar();
        });

        // Sidebar toggle functionality
        window.toggleSecondarySidebar = function () {
            const sidebar = document.getElementById('sidebar');
            const mainContent = document.getElementById('main-content');
            const toggleIcon = document.getElementById('sidebar-toggle-icon');

            if (!sidebar || !mainContent || !toggleIcon) return;

            sidebar.classList.toggle('sidebar-collapsed');

            if (sidebar.classList.contains('sidebar-collapsed')) {
                // Collapsed state
                if (window.innerWidth >= 1024) {
                    mainContent.style.marginLeft = '70px';
                    mainContent.style.width = 'calc(100vw - 70px - 20px)';
                }
                toggleIcon.classList.remove('ki-black-left-line');
                toggleIcon.classList.add('ki-black-right-line');
                localStorage.setItem('sidebar-collapsed', 'true');
            } else {
                // Expanded state
                if (window.innerWidth >= 1024) {
                    mainContent.style.marginLeft = '290px';
                    mainContent.style.width = 'calc(100vw - 290px - 20px)';
                }
                toggleIcon.classList.remove('ki-black-right-line');
                toggleIcon.classList.add('ki-black-left-line');
                localStorage.setItem('sidebar-collapsed', 'false');
            }
        };

        function initializeSidebar() {
            const isCollapsed = localStorage.getItem('sidebar-collapsed') === 'true';
            const sidebar = document.getElementById('sidebar');
            const mainContent = document.getElementById('main-content');
            const toggleIcon = document.getElementById('sidebar-toggle-icon');

            if (!sidebar || !mainContent || !toggleIcon) return;

            if (window.innerWidth >= 1024) {
                if (isCollapsed) {
                    sidebar.classList.add('sidebar-collapsed');
                    mainContent.style.marginLeft = '70px';
                    mainContent.style.width = 'calc(100vw - 70px - 20px)';
                    toggleIcon.classList.remove('ki-black-left-line');
                    toggleIcon.classList.add('ki-black-right-line');
                } else {
                    sidebar.classList.remove('sidebar-collapsed');
                    mainContent.style.marginLeft = '290px';
                    mainContent.style.width = 'calc(100vw - 290px - 20px)';
                    toggleIcon.classList.remove('ki-black-right-line');
                    toggleIcon.classList.add('ki-black-left-line');
                }
            } else {
                // Mobile - reset everything
                mainContent.style.marginLeft = '';
                mainContent.style.width = '';
            }
        }

        // Handle window resize
        window.addEventListener('resize', function () {
            initializeSidebar();
        });
    </script>
    <?php echo $__env->yieldPushContent('scripts'); ?>
</body>

</html><?php /**PATH E:\workspace\epss-revamp\resources\views/layouts/app.blade.php ENDPATH**/ ?>